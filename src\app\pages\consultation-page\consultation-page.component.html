<app-header />
<body>
  <div class="w-full h-[38rem] bg-white relative">
    <!-- Banner Background -->
    <div
      class="absolute top-0 left-0 w-full h-[38rem] z-0 rounded-4xl bg-cover bg-center"
      style="background-image: url('./Appointment.png')"
    >
      <div class="max-w-7xl h-full m-auto px-6 py-[3rem]">
        <div class="w-full h-full flex justify-between">
          <!-- ==== Contact Info (Left) ==== -->
          <div class="w-[40%] h-full">
            <h1 class="text-3xl font-bold mb-6">Contact us</h1>
            <div class="mb-6 border-b border-[#8BA6C6] pb-4">
              <h2 class="text-lg font-bold mb-2">More contact information</h2>
              <p class="mb-4 text-gray-200 text-base">
                If you have any questions about our services or need advice
                regarding gender health and mental well-being, feel free to
                reach out to us. Our team is ready to support you with any
                information or guidance you may need.
              </p>
              <div
                class="flex justify-between items-center gap-4 text-gray-200"
              >
                <div>
                  <div class="text-sm">Email address</div>
                  <div class="text-base font-semibold text-white">
                    contact&#64;medic.com
                  </div>
                </div>
                <div>
                  <div class="text-sm">Phone number</div>
                  <div class="text-base font-semibold text-white underline">
                    (253) 456 - 1189
                  </div>
                </div>
              </div>
            </div>
            <div>
              <h2 class="text-lg font-bold mb-2">Follow us</h2>
              <p class="mb-4 text-gray-200 text-base">
                Stay connected for the latest updates on gender health,
                inclusive care, and community events. Join our online community
                and let’s build a supportive and understanding environment
                together!
              </p>
              <div class="flex gap-4 mt-2">
                @for (_ of [1,2,3,4,5]; track $index) {
                <svg
                  class="w-6 h-6 text-white hover:text-pink-400 cursor-pointer transition"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M12 21C12 21 4 13.8 4 8.5C4 5.4 6.4 3 9.5 3C11.1 3 12.5 4.1 13 5.6C13.5 4.1 14.9 3 16.5 3C19.6 3 22 5.4 22 8.5C22 13.8 12 21 12 21Z"
                  />
                </svg>
                }
              </div>
            </div>
          </div>
          <!-- ==== Appointment Form (Right) ==== -->
          <div class="w-[55%] h-full flex justify-center items-center">
            <form
              #contactForm="ngForm"
              (ngSubmit)="onContactSubmit(contactForm)"
              autocomplete="off"
              class="bg-[#FFF5E7] w-full h-full flex flex-col justify-center items-center p-10 rounded-2xl shadow-lg text-black"
            >
              <!-- Row 1: Full Name & Email -->
              <div class="flex gap-4 mb-4 w-full">
                <div class="flex-1 relative">
                  <label class="block font-bold mb-1 text-black"
                    >Full name</label
                  >
                  <input
                    type="text"
                    name="fullName"
                    [(ngModel)]="contactData.fullName"
                    required
                    #fullNameModel="ngModel"
                    class="w-full px-4 py-2 rounded-xl bg-white border border-transparent focus:border-[#98d2c0] outline-none placeholder:text-gray-400"
                    placeholder="e.g. John Cater"
                    (ngModelChange)="onFieldInput(contactForm)"
                  />
                  @if ((fullNameModel.invalid && (fullNameModel.touched ||
                  formSubmitted))) {
                  <span
                    class="absolute right-0 top-0 text-xs text-red-500 font-medium"
                  >
                    Full name is required!
                  </span>
                  }
                </div>
                <div class="flex-1 relative">
                  <label class="block font-bold mb-1 text-black"
                    >Email address</label
                  >
                  <input
                    type="email"
                    name="email"
                    [(ngModel)]="contactData.email"
                    required
                    email
                    #emailModel="ngModel"
                    class="w-full px-4 py-2 rounded-xl bg-white border border-transparent focus:border-[#98d2c0] outline-none placeholder:text-gray-400"
                    placeholder="e.g. <EMAIL>"
                    (ngModelChange)="onFieldInput(contactForm)"
                  />
                  @if ((emailModel.invalid && (emailModel.touched ||
                  formSubmitted))) { @if (emailModel.errors?.['required']) {
                  <span
                    class="absolute right-0 top-0 text-xs text-red-500 font-medium"
                  >
                    Email is required!
                  </span>
                  } @if (emailModel.errors?.['email']) {
                  <span
                    class="absolute right-0 top-0 text-xs text-red-500 font-medium"
                  >
                    Email format is invalid!
                  </span>
                  } }
                </div>
              </div>

              <!-- Row 2: Phone & Schedule -->
              <div class="flex gap-4 mb-4 w-full">
                <div class="flex-1 relative">
                  <label class="block font-bold mb-1 text-black"
                    >Phone number</label
                  >
                  <input
                    type="text"
                    name="phone"
                    [(ngModel)]="contactData.phone"
                    required
                    pattern="^(\(\d{3}\)\s?\d{3}\s?-\s?\d{4}|0\d{9,10})$"
                    #phoneModel="ngModel"
                    class="w-full px-4 py-2 rounded-xl bg-white border border-transparent focus:border-[#98d2c0] outline-none placeholder:text-gray-400"
                    placeholder="e.g. (123) 456 - 7890"
                    (ngModelChange)="onFieldInput(contactForm)"
                  />
                  @if ((phoneModel.invalid && (phoneModel.touched ||
                  formSubmitted))) { @if (phoneModel.errors?.['required']) {
                  <span
                    class="absolute right-0 top-0 text-xs text-red-500 font-medium"
                  >
                    Phone number is required!
                  </span>
                  } @if (phoneModel.errors?.['pattern']) {
                  <span
                    class="absolute right-0 top-0 text-xs text-red-500 font-medium"
                  >
                    Invalid phone number!
                  </span>
                  } }
                </div>
                <div class="flex-1">
                  <label class="block font-bold mb-1 text-black"
                    >Schedule to receive call</label
                  >
                  <div class="flex flex-col gap-2">
                    <label class="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="schedule"
                        [(ngModel)]="contactData.schedule"
                        value="Anytime"
                        class="accent-[#98d2c0]"
                        (ngModelChange)="onFieldInput(contactForm)"
                      />
                      <span class="text-base text-[#4E6688]">Anytime</span>
                    </label>
                    <label class="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="schedule"
                        [(ngModel)]="contactData.schedule"
                        value="Office hours (8:00 AM - 5:00 PM)"
                        class="accent-[#98d2c0]"
                        (ngModelChange)="onFieldInput(contactForm)"
                      />
                      <span class="text-base text-[#4E6688]"
                        >Office hours (8:00 AM - 5:00 PM)</span
                      >
                    </label>
                    <label class="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="schedule"
                        [(ngModel)]="contactData.schedule"
                        value="Outside office hours (5:00 PM - 10:00 PM)"
                        class="accent-[#98d2c0]"
                        (ngModelChange)="onFieldInput(contactForm)"
                      />
                      <span class="text-base text-[#4E6688]"
                        >Outside office hours (5:00 PM - 10:00 PM)</span
                      >
                    </label>
                  </div>
                </div>
              </div>

              <!-- Message -->
              <div class="mb-6 w-full relative">
                <label class="block font-bold mb-1 text-black">Message</label>
                <textarea
                  name="message"
                  [(ngModel)]="contactData.message"
                  required
                  #messageModel="ngModel"
                  class="w-full h-18 px-4 py-2 rounded-xl bg-white border border-transparent focus:border-[#98d2c0] outline-none resize-none placeholder:text-gray-400"
                  placeholder="Type your message here (e.g. If you have any specific questions or need any information, please feel free to write it here...)"
                  (ngModelChange)="onFieldInput(contactForm)"
                ></textarea>
                @if ((messageModel.invalid && (messageModel.touched ||
                formSubmitted))) {
                <span
                  class="absolute right-0 top-0 text-xs text-red-500 font-medium"
                >
                  Message is required!
                </span>
                }
              </div>

              <!-- Remember + Submit -->
              <div class="flex items-center mb-4 w-full">
                <input
                  type="checkbox"
                  [(ngModel)]="RememberContact"
                  name="rememberContact"
                  class="mr-2"
                />
                <span class="text-sm text-gray-600">Remember this info</span>
              </div>

              <button
                type="submit"
                class="cursor-pointer relative py-3 px-6 text-white text-base font-bold nded-full overflow-hidden bg-[#98d2c0] rounded-4xl transition-all duration-200 ease-in-out shadow-md hover:scale-105 hover:text-[#98d2c0] hover:shadow-lg active:scale-90 before:absolute before:top-0 before:-left-full before:w-full before:h-full before:bg-gradient-to-r before:from-white before:to-white before:transition-all before:duration-300 before:ease-in-out before:z-[-1] before:rounded-4xl hover:before:left-0"
              >
                @if (!isSubmitting) { Send message } @if (isSubmitting) {
                <span class="flex items-center gap-2">
                  <span
                    class="loader w-5 h-5 border-2 border-t-[#98d2c0] border-[#FFF5E7] rounded-full animate-spin"
                  ></span>
                  Sending...
                </span>
                }
              </button>
            </form>
          </div>
          <!-- ==== END FORM ==== -->
        </div>
      </div>
    </div>
    <!-- End Banner -->
  </div>
</body>
<app-footer />

/* Modern Dark Chat Interface */
.tgdd-chat-container {
  /* position: fixed; */
  bottom: 20px;
  right: 1.2rem;
  /* z-index: 9999; */
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", sans-serif;
}

/* Main Chat Panel */
.tgdd-chat-panel {
  position: fixed;
  bottom: 0;
  right: 1.2rem;
  display: flex;
  flex-direction: column;
  width: 400px;
  height: 600px;
  max-height: 100vh;
  background: #ffffff;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -10px 25px -5px rgba(78, 102, 136, 0.15),
    0 -4px 6px -1px rgba(78, 102, 136, 0.1), 0 0 0 1px rgba(78, 102, 136, 0.05);
  border: 1px solid rgba(78, 102, 136, 0.1);
  border-bottom: none;
  overflow: hidden;
  transform: translateY(100%) scale(0.8);
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  pointer-events: none;
  /* z-index: 10000; */
}

.tgdd-chat-panel.active {
  transform: translateY(0) scale(1);
  opacity: 1;
  pointer-events: all;
}

/* Backdrop blur effect when chat is open */
.copilot-chat-container::before {
  content: "";
  /* position: fixed; */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(2px);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  /* z-index: 9998; */
}

.copilot-chat-panel.active ~ .copilot-chat-container::before {
  opacity: 1;
  pointer-events: all;
}

@media (max-width: 640px) {
  .tgdd-chat-panel {
    width: 100vw;
    height: 100vh;
    max-width: none;
    right: 0;
    left: 0;
    bottom: 0;
    border-radius: 0;
    border: none;
  }
}

/* Header */
.tgdd-header {
  background: linear-gradient(135deg, #4e6688 0%, #5a7394 100%);
  color: #ffffff;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e5e7eb;
  min-height: 60px;
  box-shadow: 0 2px 4px rgba(78, 102, 136, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.support-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    rgba(78, 102, 136, 0.12) 0%,
    rgba(88, 215, 254, 0.08) 100%
  );
  border: 2px solid rgba(78, 102, 136, 0.25);
  box-shadow: 0 0 0 2px rgba(78, 102, 136, 0.1),
    0 2px 4px rgba(78, 102, 136, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.support-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 0 0 2px rgba(78, 102, 136, 0.2),
    0 4px 8px rgba(78, 102, 136, 0.25);
}

.support-icon {
  display: block;
}

.header-title {
  font-weight: 600;
  font-size: 14px;
  margin: 0;
  color: #ffffff;
  letter-spacing: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* .action-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: transparent;
  color: #ffffff;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  opacity: 0.8;
} */

.action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  opacity: 1;
}

.action-btn:active {
  transform: scale(0.9);
}

/* Chat Body */
.tgdd-chat-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px 24px;
  background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
  display: flex;
  flex-direction: column;
}

/* Connection Alert */
.connection-alert {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 16px;
}

.alert-icon {
  flex-shrink: 0;
}

/* Chat Conversation */
.chat-conversation {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
}

.message-container {
  display: flex;
  flex-direction: column;
  animation: slideInMessage 0.3s ease-out;
}

@keyframes slideInMessage {
  from {
    opacity: 0;
    transform: translateY(15px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
    box-shadow: 0 8px 25px -5px rgba(78, 102, 136, 0.4),
      0 4px 6px -2px rgba(78, 102, 136, 0.2);
  }
  14% {
    transform: scale(1.05);
    box-shadow: 0 10px 30px -5px rgba(78, 102, 136, 0.5),
      0 6px 8px -2px rgba(78, 102, 136, 0.3);
  }
  28% {
    transform: scale(1);
    box-shadow: 0 8px 25px -5px rgba(78, 102, 136, 0.4),
      0 4px 6px -2px rgba(78, 102, 136, 0.2);
  }
  42% {
    transform: scale(1.05);
    box-shadow: 0 10px 30px -5px rgba(78, 102, 136, 0.5),
      0 6px 8px -2px rgba(78, 102, 136, 0.3);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 8px 25px -5px rgba(78, 102, 136, 0.4),
      0 4px 6px -2px rgba(78, 102, 136, 0.2);
  }
}

.message {
  display: flex;
  gap: 12px;
  max-width: 85%;
  align-items: flex-start;
}

.message-bot {
  align-self: flex-start;
  margin-left: 0;
  padding-left: 0;
}

.message-user {
  align-self: flex-end;
  flex-direction: row-reverse;
  margin-right: 0;
  padding-right: 0;
}

.message-avatar {
  flex-shrink: 0;
  margin-top: 2px;
  margin-right: 8px;
}

.bot-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    rgba(78, 102, 136, 0.1) 0%,
    rgba(88, 215, 254, 0.05) 100%
  );
  border: 2px solid rgba(78, 102, 136, 0.2);
  box-shadow: 0 0 0 1px rgba(78, 102, 136, 0.08),
    0 2px 4px rgba(78, 102, 136, 0.12);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.bot-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 0 0 1px rgba(78, 102, 136, 0.15),
    0 3px 6px rgba(78, 102, 136, 0.2);
}

.bot-support-icon {
  display: block;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-text {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 18px;
  padding: 12px 16px;
  font-size: 14px;
  line-height: 1.5;
  color: #374151;
  word-wrap: break-word;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.08);
  max-width: 300px;
  transition: all 0.2s ease;
}

.message-text:hover {
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.12);
}

.message-user .message-text {
  background: linear-gradient(135deg, #4e6688 0%, #5a7394 100%);
  color: #ffffff;
  border-color: transparent;
  box-shadow: 0 4px 6px -1px rgba(78, 102, 136, 0.3);
}

.message-user .message-text:hover {
  box-shadow: 0 6px 12px -1px rgba(78, 102, 136, 0.4);
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 6px;
  padding: 0 4px;
}

.message-time {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
}

.message-sender {
  font-size: 11px;
  color: #6b7280;
  font-weight: 600;
}

/* Typing Indicator */
.typing-container {
  display: flex;
  flex-direction: column;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #9ca3af;
  animation: typing-bounce 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0s;
}

@keyframes typing-bounce {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.typing-text {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
}

/* Quick Suggestions */
.quick-suggestions {
  margin-top: 16px;
  padding: 0 4px;
}

.suggestions-label {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.suggestion-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggestion-chip {
  background: #ffffff;
  color: #4e6688;
  border: 1px solid #e5e7eb;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.05);
}

.suggestion-chip:hover {
  background: linear-gradient(135deg, #4e6688 0%, #5a7394 100%);
  color: #ffffff;
  border-color: #4e6688;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px 0 rgba(78, 102, 136, 0.25);
}

.suggestion-chip:active {
  transform: translateY(0);
}

/* Input Section */
.tgdd-input-section {
  background: #ffffff;
  border-top: 1px solid #e5e7eb;
  padding: 16px 24px;
}

.input-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-wrapper.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.input-field {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  background: #f9fafb;
  border: 1px solid #d1d5db;
  border-radius: 25px;
  padding: 10px 16px;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.05);
}

.input-field:focus-within {
  border-color: #4e6688;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(78, 102, 136, 0.1), 0 2px 4px 0 rgba(0, 0, 0, 0.1);
}

.message-input {
  flex: 1;
  resize: none;
  border: none;
  outline: none;
  background: transparent;
  font-size: 14px;
  line-height: 1.5;
  color: #374151;
  min-height: 20px;
  max-height: 120px;
  font-family: inherit;
}

.message-input::placeholder {
  color: #9ca3af;
}

.send-button {
  width: 36px;
  height: 36px;
  border: none;
  background: linear-gradient(135deg, #4e6688 0%, #5a7394 100%);
  color: #ffffff;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(78, 102, 136, 0.3);
}

.send-button:hover:not(:disabled) {
  transform: scale(1.1);
  background: linear-gradient(135deg, #5a7394 0%, #6b82a6 100%);
  box-shadow: 0 4px 8px rgba(78, 102, 136, 0.4);
}

.send-button:active:not(:disabled) {
  transform: scale(0.95);
}

.send-button:disabled,
.send-button.sending {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 4px;
}

.input-hint {
  font-size: 11px;
  color: #9ca3af;
  font-weight: 500;
}

/* Floating Action Button */
.tgdd-fab {
  /* position: fixed; */
  bottom: 20px;
  right: 1.2rem;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #4e6688 0%, #5a7394 100%);
  border-radius: 50%;
  box-shadow: 0 8px 25px -5px rgba(78, 102, 136, 0.4),
    0 4px 6px -2px rgba(78, 102, 136, 0.2);
  cursor: pointer;
  /* display: flex;
  align-items: center;
  justify-content: center; */
  /* z-index: 10001; */
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  color: #ffffff;
  /* position: relative; */
  border: 2px solid #ffffff;
  opacity: 1;
  transform: scale(1);
  animation: heartbeat 2s ease-in-out infinite;
}

.tgdd-fab:hover {
  transform: scale(1.1);
  box-shadow: 0 12px 30px -5px rgba(78, 102, 136, 0.5),
    0 6px 8px -2px rgba(78, 102, 136, 0.3);
  background: linear-gradient(135deg, #5a7394 0%, #6b82a6 100%);
}

.tgdd-fab:active {
  transform: scale(0.9);
}

.tgdd-fab.active {
  opacity: 0;
  transform: scale(0.3);
  pointer-events: none;
}

.tgdd-fab.has-unread {
  animation: pulse-notification 2s infinite;
}

@keyframes pulse-notification {
  0%,
  100% {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05), 0 0 0 0 rgba(102, 126, 234, 0.7);
  }
  50% {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05), 0 0 0 8px rgba(102, 126, 234, 0);
  }
}

.fab-content {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
  width: 100%;
  height: 100%;
}

.fab-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.fab-support-icon {
  display: block;
  transition: all 0.2s ease;
}

.tgdd-fab:hover .fab-support-icon {
  transform: scale(1.05);
}

.unread-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #4e6688;
  color: #ffffff;
  border-radius: 10px;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 600;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(78, 102, 136, 0.3);
}

/* Scrollbar */
.tgdd-chat-body::-webkit-scrollbar {
  width: 6px;
}

.tgdd-chat-body::-webkit-scrollbar-track {
  background: transparent;
}

.tgdd-chat-body::-webkit-scrollbar-thumb {
  background: linear-gradient(
    135deg,
    rgba(78, 102, 136, 0.3) 0%,
    rgba(78, 102, 136, 0.2) 100%
  );
  border-radius: 3px;
}

.tgdd-chat-body::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    135deg,
    rgba(78, 102, 136, 0.5) 0%,
    rgba(78, 102, 136, 0.4) 100%
  );
}

.tgdd-chat-body {
  scrollbar-width: thin;
  scrollbar-color: rgba(78, 102, 136, 0.3) transparent;
}

/* Responsive Design */
@media (max-width: 640px) {
  .tgdd-fab {
    width: 45px;
    height: 45px;
    bottom: 16px;
    right: 20px;
  }

  .copilot-chat-panel {
    width: 100vw !important;
    height: 100vh !important;
    right: 0 !important;
    left: 0 !important;
    bottom: 0 !important;
    max-height: 100vh !important;
    border-radius: 0 !important;
    border: none !important;
  }

  .tgdd-chat-body {
    padding: 12px 16px;
  }

  .tgdd-header {
    padding: 10px 12px;
  }

  .tgdd-input-section {
    padding: 10px 16px;
  }

  .message-bot {
    margin-left: 0;
  }

  .message-user {
    margin-right: 0;
  }

  .message-text {
    font-size: 12px;
    padding: 8px 10px;
    max-width: 240px;
  }

  .suggestion-chip {
    font-size: 11px;
    padding: 4px 8px;
  }

  .support-avatar {
    width: 32px;
    height: 32px;
  }

  .bot-avatar {
    width: 28px;
    height: 28px;
  }

  .support-icon {
    width: 16px;
    height: 16px;
  }

  .bot-support-icon {
    width: 14px;
    height: 14px;
  }

  .fab-support-icon {
    width: 20px;
    height: 20px;
  }
}

/* Tablet Design */
@media (max-width: 768px) and (min-width: 641px) {
  .tgdd-chat-panel {
    width: 380px;
    height: 550px;
    right: 1rem;
    bottom: 0;
    border-radius: 12px 12px 0 0;
  }

  /* .tgdd-fab {
    bottom: 16px;
    right: 1rem;
  } */
}

/* Large screens */
@media (min-width: 1200px) {
  .tgdd-chat-container {
    right: 1.5rem;
    bottom: 0;
  }

  .tgdd-fab {
    right: 1.5rem;
    bottom: 30px;
  }

  .tgdd-chat-panel {
    right: 1.5rem;
    bottom: 0;
  }
}

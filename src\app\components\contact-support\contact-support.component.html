<!-- Contact Support Buttons - Positioned above AI chat -->
<div class="contact-support-container">
  <!-- Phone Button -->
  <button
    (click)="onPhoneClick()"
    class="contact-btn phone-btn"
    [title]="'CONTACT_SUPPORT.PHONE_TITLE' | translate"
  >
    <!-- Phone Icon -->
    <svg class="contact-icon" fill="currentColor" viewBox="0 0 24 24">
      <path
        d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z"
      />
    </svg>

    <!-- Ripple Effect -->
    <div class="ripple"></div>

    <!-- Tooltip -->
    <div class="tooltip">
      {{ "CONTACT_SUPPORT.PHONE_TITLE" | translate }}: {{ phoneNumber }}
    </div>
  </button>

  <!-- Zalo Button -->
  <button
    (click)="onZaloClick()"
    class="contact-btn zalo-btn"
    [title]="'CONTACT_SUPPORT.ZALO_TITLE' | translate"
  >
    <!-- Zalo Icon - Real Logo -->
    <img
      src="https://cdn-icons-png.flaticon.com/512/5968/5968841.png"
      alt="Zalo"
      class="contact-icon"
      style="filter: brightness(0) invert(1)"
    />

    <!-- Ripple Effect -->
    <div class="ripple"></div>

    <!-- Tooltip -->
    <div class="tooltip">
      {{ "CONTACT_SUPPORT.ZALO_CHAT" | translate }}
    </div>
  </button>
</div>

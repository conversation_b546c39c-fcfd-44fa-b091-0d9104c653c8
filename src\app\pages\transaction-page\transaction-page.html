<app-header />
<body class="bg-white">
  <!-- ======= BREADCRUMBS ======= -->
  <!-- <div class="max-w-7xl mx-auto px-6 pt-7 text-[#4E6688]">
    <app-breadcrumbs />
  </div> -->

  <div class="transaction-container">
    <div class="transaction-header">
      <h1 class="page-title">
        <i class="pi pi-credit-card"></i>
        Thanh toán dịch vụ
      </h1>
      <p class="page-subtitle">
        Hoàn tất thanh toán để sử dụng dịch vụ chăm sóc sức khỏe giới tính.
      </p>
    </div>

    <div class="transaction-content">
      <!-- Order Summary -->
      <div class="order-summary">
        <h2 class="section-title">
          <i class="pi pi-list"></i>
          Tóm tắt đơn hàng
        </h2>

        <div class="order-items">
          <div class="order-item" *ngFor="let item of cart.items">
            <div class="item-image">
              <img
                [src]="item.image_link || '/assets/images/default-service.jpg'"
                [alt]="item.service_name"
                onerror="this.src='/assets/images/default-service.jpg'"
              />
            </div>
            <div class="item-details">
              <h4 class="item-name">{{ item.service_name }}</h4>
              <div class="item-meta">
                <span class="quantity">Số lượng: {{ item.quantity }}</span>
                <span class="price">{{ formatPrice(item.price) }}</span>
              </div>
            </div>
            <div class="item-total">{{ formatPrice(getItemTotal(item)) }}</div>
          </div>
        </div>

        <div class="order-total">
          <div class="total-row">
            <span class="total-label">Tổng cộng:</span>
            <span class="total-amount">{{ formatPrice(cart.total) }}</span>
          </div>
        </div>
      </div>

      <!-- Customer Information -->
      <div class="customer-info">
        <h2 class="section-title">
          <i class="pi pi-user"></i>
          Thông tin khách hàng
        </h2>

        <form class="customer-form" (ngSubmit)="processPayment()">
          <div class="form-group">
            <label for="fullName">Họ và tên *</label>
            <input
              type="text"
              id="fullName"
              [(ngModel)]="customerInfo.fullName"
              name="fullName"
              class="form-control"
              placeholder="Nhập họ và tên đầy đủ."
              required
            />
          </div>

          <div class="form-group">
            <label for="email">Email *</label>
            <input
              type="email"
              id="email"
              [(ngModel)]="customerInfo.email"
              name="email"
              class="form-control"
              placeholder="Nhập địa chỉ email."
              required
            />
          </div>

          <div class="form-group">
            <label for="phone">Số điện thoại *</label>
            <input
              type="tel"
              id="phone"
              [(ngModel)]="customerInfo.phone"
              name="phone"
              class="form-control"
              placeholder="Nhập số điện thoại."
              required
            />
          </div>

          <div class="form-group">
            <label for="address">Địa chỉ</label>
            <textarea
              id="address"
              [(ngModel)]="customerInfo.address"
              name="address"
              class="form-control"
              rows="3"
              placeholder="Nhập địa chỉ (tùy chọn)."
            ></textarea>
          </div>

          <!-- Error Message -->
          <div class="error-message" *ngIf="errorMessage">
            <i class="pi pi-exclamation-triangle"></i>
            {{ errorMessage }}
          </div>

          <!-- Payment Method -->
          <div class="payment-method">
            <h3 class="payment-title">
              <i class="pi pi-credit-card"></i>
              Phương thức thanh toán
            </h3>
            <div class="vnpay-info">
              <img src="./vnpay-logo.jpg" alt="VNPay" class="vnpay-logo" />
              <div class="vnpay-description">
                <h4>VNPay</h4>
                <p>
                  Thanh toán an toàn qua cổng thanh toán VNPay với các ngân hàng
                  trong nước.
                </p>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="form-actions">
            <button type="button" class="btn btn-secondary" routerLink="/cart">
              <i class="pi pi-arrow-left"></i>
              Quay lại giỏ hàng
            </button>

            <button
              type="submit"
              class="btn btn-primary"
              [disabled]="isProcessing"
            >
              <i class="pi pi-spin pi-spinner" *ngIf="isProcessing"></i>
              <i class="pi pi-credit-card" *ngIf="!isProcessing"></i>
              {{ isProcessing ? 'Đang xử lý...' : 'Thanh toán ngay' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</body>

<app-footer />

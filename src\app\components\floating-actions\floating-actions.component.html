<!-- Floating Action Buttons - All 4 buttons in vertical stack -->
<div class="floating-actions-container">
  <!-- 1. Phone Button -->
  <div class="action-item">
    <button
      (click)="onPhoneClick()"
      class="action-btn phone-btn"
      [title]="'CONTACT_SUPPORT.PHONE_TITLE' | translate"
    >
      <svg class="action-icon" fill="currentColor" viewBox="0 0 24 24">
        <path
          d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z"
        />
      </svg>
      <div class="ripple-effect"></div>
    </button>

    <!-- Hover tooltip -->
    <div class="tooltip">
      <span class="tooltip-text">{{
        "CONTACT_SUPPORT.PHONE_TITLE" | translate
      }}</span>
      <span class="tooltip-number">{{ phoneNumber }}</span>
    </div>
  </div>

  <!-- 2. Zalo Button -->
  <div class="action-item">
    <button
      (click)="onZaloClick()"
      class="action-btn zalo-btn"
      [title]="'CONTACT_SUPPORT.ZALO_TITLE' | translate"
    >
      <img
        src="https://cdn-icons-png.flaticon.com/512/5968/5968841.png"
        alt="Zalo"
        class="action-icon zalo-icon"
      />
      <div class="ripple-effect"></div>
    </button>

    <!-- Hover tooltip -->
    <div class="tooltip">
      <span class="tooltip-text">{{
        "CONTACT_SUPPORT.ZALO_TITLE" | translate
      }}</span>
      <span class="tooltip-number">{{
        "CONTACT_SUPPORT.ZALO_CHAT" | translate
      }}</span>
    </div>
  </div>

  <!-- 3. AI Support Chat Button -->
  <div class="action-item">
    <!-- Headset Icon from Flaticon -->
    <div class="ripple-effect"></div>
    <app-support-chat />
    <!-- Hover tooltip -->
    <!-- <div class="tooltip">
      <span class="tooltip-text">{{ "AI_CHAT.TITLE" | translate }}</span>
    </div> -->
  </div>

  <!-- 4. Back to Top Button -->
  @if (showBackToTop()) {
  <div class="action-item">
    <button
      (click)="scrollToTop()"
      class="action-btn back-to-top-btn"
      [title]="'BACK_TO_TOP.TITLE' | translate"
    >
      <svg
        class="action-icon"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M5 15l7-7 7 7"
        />
      </svg>
      <div class="ripple-effect"></div>
    </button>

    <!-- Hover tooltip -->
    <div class="tooltip">
      <span class="tooltip-text">{{ "BACK_TO_TOP.TITLE" | translate }}</span>
    </div>
  </div>
  }
</div>

<!-- AI Chat Interface (when opened) -->
@if (isChatOpen()) {
<div class="chat-overlay" (click)="closeChat()">
  <div class="chat-container" (click)="$event.stopPropagation()">
    <div class="chat-header">
      <div class="chat-title">
        <img
          src="https://cdn-icons-png.flaticon.com/512/2706/2706962.png"
          alt="AI Support"
          class="w-5 h-5 mr-2 headset-icon-header"
        />
        {{ "AI_CHAT.TITLE" | translate }}
      </div>
      <button (click)="closeChat()" class="close-btn">
        <svg
          class="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>

    <div class="chat-content">
      <div class="chat-message bot-message">
        <div class="message-content">
          {{ "AI_CHAT.WELCOME" | translate }}
        </div>
      </div>

      <div class="quick-replies">
        <div class="quick-reply-title">
          {{ "AI_CHAT.QUICK_REPLIES_TITLE" | translate }}
        </div>
        @for (reply of getQuickReplies(); track reply; let i = $index) {
        <button class="quick-reply-btn" (click)="sendQuickReply(reply)">
          {{ reply }}
        </button>
        }
      </div>
    </div>

    <div class="chat-input">
      <input
        type="text"
        [placeholder]="'AI_CHAT.PLACEHOLDER' | translate"
        class="message-input"
      />
      <button class="send-btn">
        <svg
          class="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
          />
        </svg>
      </button>
    </div>
  </div>
</div>
}

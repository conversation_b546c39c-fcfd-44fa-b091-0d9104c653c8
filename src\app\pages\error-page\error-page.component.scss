// Error Page Styles
.error-page-container {
  font-family: "Source Sans Pro", Arial, sans-serif;
  background-color: #f8f9fa;
  min-height: 100vh;
}

// Language Selector Styles
.language-selector {
  img {
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    &.active {
      transform: scale(1.02);
    }
  }
}

// Hanging System Styles
.hanging-system {
  position: absolute;
  top: -60px;
  left: 50%;
  transform: translateX(-50%);
  width: 300px;
  height: 60px;
}

.top-hook {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 12px;
  height: 12px;
  background: #333;
  border-radius: 50%;
  border: 2px solid #555;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
  z-index: 10;
}

.rope {
  position: absolute;
  width: 3px;
  background: #333;
  transform-origin: top center;

  &.left-rope {
    top: 12px;
    left: calc(50% - 1.5px);
    height: 48px;
    transform: rotate(-45deg);
    transform-origin: top center;
  }

  &.right-rope {
    top: 12px;
    left: calc(50% - 1.5px);
    height: 48px;
    transform: rotate(45deg);
    transform-origin: top center;
  }
}

.side-hook {
  position: absolute;
  bottom: 0;
  width: 8px;
  height: 8px;
  background: #333;
  border-radius: 50%;
  border: 1px solid #555;

  &.left-hook {
    left: calc(50% - 34px);
  }

  &.right-hook {
    right: calc(50% - 34px);
  }
}

.monitor-container {
  position: relative;
  display: inline-block;
  animation: gentle-swing 3s ease-in-out infinite;
}

.monitor-frame {
  width: 320px;
  height: 240px;
  background: #fff;
  border: 3px solid #4e6688;
  border-radius: 8px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3), inset 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.browser-bar {
  height: 40px;
  background: #f8f9fa;
  border-bottom: 2px solid #4e6688;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}

.browser-controls {
  display: flex;
  align-items: center;
}

.hamburger-menu {
  display: flex;
  flex-direction: column;
  gap: 2px;

  .line {
    width: 12px;
    height: 2px;
    background: #4e6688;
    border-radius: 1px;
  }
}

.browser-dots {
  display: flex;
  gap: 6px;

  .dot {
    width: 8px;
    height: 8px;
    background: #4e6688;
    border-radius: 50%;
  }
}

.monitor-screen {
  width: 100%;
  height: calc(100% - 40px);
  background: #fff;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-display {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.error-code {
  color: #4e6688;
  font-size: 4.5rem;
  font-weight: bold;
  font-family: "Arial", sans-serif;
  text-shadow: 0 2px 4px rgba(78, 102, 136, 0.3);
  animation: text-glow 2s ease-in-out infinite alternate;
  letter-spacing: 0.1em;
}

// Animations
@keyframes gentle-swing {
  0%,
  100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(1deg);
  }
  75% {
    transform: rotate(-1deg);
  }
}

@keyframes text-glow {
  from {
    text-shadow: 0 2px 4px rgba(78, 102, 136, 0.3);
  }
  to {
    text-shadow: 0 4px 8px rgba(78, 102, 136, 0.6);
  }
}

// Button Styles
.error-page-container button {
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 640px) {
  .monitor-frame {
    width: 280px;
    height: 200px;
  }

  .error-code {
    font-size: 3rem;
  }

  .hanging-system {
    width: 250px;
    top: -50px;
    height: 50px;
  }

  .rope {
    &.left-rope,
    &.right-rope {
      height: 38px;
    }
  }

  .side-hook {
    &.left-hook {
      left: calc(50% - 27px);
    }

    &.right-hook {
      right: calc(50% - 27px);
    }
  }
}

// Focus states for accessibility
button:focus {
  outline: 2px solid #4e6688;
  outline-offset: 2px;
}

.cart-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.cart-title {
  color: #2c5aa0;
  font-size: 2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
}

.cart-count {
  background: #e91e63;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Empty Cart Styles */
.empty-cart {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.empty-cart-icon {
  font-size: 4rem;
  color: #ddd;
  margin-bottom: 1rem;
}

.empty-cart h3 {
  color: #333;
  margin-bottom: 1rem;
}

.empty-cart p {
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

/* Cart Items */
.cart-items {
  margin-bottom: 2rem;
}

.cart-item {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  margin-bottom: 1rem;
  background: #fafafa;
  transition: all 0.3s ease;
}

.cart-item:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.item-image {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-details {
  flex: 1;
  min-width: 0;
}

.item-name {
  color: #2c5aa0;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.item-description {
  color: #666;
  font-size: 0.9rem;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.item-duration {
  color: #888;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.item-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  border: 1px solid #000000;
  border-radius: 8px;
  padding: 0.25rem;
}

.quantity-btn {
  background: none;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #000000;
}

.quantity-btn:hover:not(:disabled) {
  background: #f0f0f0;
  color: #2c5aa0;
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity {
  min-width: 40px;
  text-align: center;
  font-weight: 600;
  color: #333;
}

.item-price {
  text-align: right;
  min-width: 120px;
}

.unit-price {
  color: #666;
  font-size: 0.9rem;
}

.total-price {
  display: block;
  color: #2c5aa0;
  font-weight: 600;
  font-size: 1.1rem;
}

.remove-btn {
  background: none;
  border: none;
  color: #e91e63;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.remove-btn:hover {
  background: #fce4ec;
  transform: scale(1.1);
}

/* Cart Summary */
.cart-summary {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 2rem;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.summary-row:last-child {
  margin-bottom: 0;
}

.summary-label {
  color: #666;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.summary-value {
  font-weight: 600;
  color: #333;
}

.summary-row.total {
  border-top: 2px solid #ddd;
  padding-top: 0.75rem;
  margin-top: 0.75rem;
}

.summary-row.total .summary-label,
.summary-row.total .summary-value {
  font-size: 1.2rem;
  font-weight: 700;
  color: #2c5aa0;
}

/* Cart Actions */
.cart-actions {
  display: flex;
  gap: 1rem;
  justify-content: space-between;
  flex-wrap: wrap;
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.btn-primary {
  background: #2c5aa0;
  color: white;
}

.btn-primary:hover {
  background: #1e3f73;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(44, 90, 160, 0.3);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-outline {
  background: transparent;
  color: #2c5aa0;
  border: 2px solid #2c5aa0;
}

.btn-outline:hover {
  background: #2c5aa0;
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .cart-container {
    padding: 1rem;
  }
  
  .cart-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .cart-item {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .item-controls {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .cart-actions {
    flex-direction: column;
  }
  
  .btn {
    justify-content: center;
  }
}

<app-header />

<body class="bg-[#f9fafb] text-black font-sans">
  <div class="max-w-7xl mx-auto px-6 pt-7 text-[#4E6688]">
    <app-breadcrumbs />
  </div>
  <div class="max-w-7xl mx-auto py-12 px-6">
    <!-- Tiêu đề -->
    <h2 class="text-4xl font-bold text-center mb-4 text-[#1e3a8a]">
      {{ "DOCTOR.HEADLINE" | translate }}
    </h2>
    <p
      class="text-center text-gray-500 max-w-2xl mx-auto mb-10 text-lg leading-relaxed"
    >
      {{ "DOCTOR.DESCRIPTION" | translate }}
    </p>

    <!-- Filter + List -->
    <div class="flex flex-col md:flex-row gap-8 mb-12">
      <!-- Toggle filter button (mobile only) -->
      <button
        class="md:hidden mb-4 px-4 py-2 bg-blue-600 text-white rounded-md shadow transition hover:bg-blue-700"
        (click)="showFilter = !showFilter"
      >
        {{
          showFilter
            ? ("DOCTOR.FILTER.HIDE" | translate)
            : ("DOCTOR.FILTER.SHOW" | translate)
        }}
      </button>

      <!-- Filter Sidebar -->
      <div
        class="md:w-1/4 w-full space-y-4 bg-white px-6 py-5 rounded-xl shadow-md backdrop-blur-sm transition-all duration-500 ease-in-out"
        [ngClass]="{
          'max-h-[1000px] opacity-100 translate-y-0': showFilter || isDesktop,
          'max-h-0 opacity-0 -translate-y-4 overflow-hidden':
            !showFilter && !isDesktop
        }"
      >
        <input
          type="text"
          [placeholder]="'DOCTOR.FILTER.SEARCH_PLACEHOLDER' | translate"
          class="w-full px-4 py-2 rounded-xl bg-gray-50 shadow-inner focus:ring-2 ring-blue-400 focus:scale-[1.02] transition-all duration-300"
          (input)="onSearch($event)"
          [value]="searchValue"
        />

        <select
          class="w-full px-4 py-2 rounded-xl bg-gray-50 shadow-inner focus:ring-2 ring-blue-400 focus:scale-[1.02] transition-all duration-300"
          [(ngModel)]="selectedSpecialty"
          (ngModelChange)="selectSpecialty($event)"
        >
          @for (spec of specialties; track spec) {
          <option [value]="spec">
            {{
              spec === "All"
                ? ("DOCTOR.FILTER.ALL_SPECIALTIES" | translate)
                : (spec.replaceAll("_", " ") | titlecase)
            }}
          </option>
          }
        </select>

        <select
          class="w-full px-4 py-2 rounded-xl bg-gray-50 shadow-inner focus:ring-2 ring-blue-400 focus:scale-[1.02] transition-all duration-300"
          [(ngModel)]="selectedGender"
          (ngModelChange)="selectGender($event)"
        >
          @for (g of genders; track g) {
          <option [value]="g">
            {{
              g === "All"
                ? ("DOCTOR.FILTER.ALL_GENDERS" | translate)
                : ("DOCTOR.GENDER." + g | translate)
            }}
          </option>
          }
        </select>
      </div>

      <!-- Danh sách bác sĩ -->
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-10 flex-1">
        @if (loading) { @for (i of [1, 2, 3]; track i) {
        <!-- Skeleton loading -->
        <div
          class="animate-pulse w-full aspect-[2/3] rounded-3xl bg-gradient-to-br from-gray-200 to-gray-300"
        >
          <div class="w-full h-2/3 bg-gray-300 rounded-t-3xl"></div>
          <div class="p-4 space-y-3">
            <div class="h-4 bg-gray-300 rounded w-3/4"></div>
            <div class="h-3 bg-gray-300 rounded w-1/2"></div>
            <div class="flex gap-2 mt-2">
              <div class="w-16 h-4 bg-gray-300 rounded-full"></div>
              <div class="w-20 h-4 bg-gray-300 rounded-full"></div>
            </div>
          </div>
        </div>
        } } @else if (paginatedDoctors.length > 0) { @for (doc of
        paginatedDoctors; track doc.doctor_id) {
        <a
          [routerLink]="['/doctor', doc.doctor_id]"
          class="group relative w-full aspect-[2/3] overflow-hidden rounded-3xl shadow-md hover:shadow-xl transition-all duration-500 bg-gradient-to-br from-white to-gray-100 block"
          data-aos="fade-up"
          data-aos-delay="100"
        >
          <img
            [src]="getImageUrl(doc.staff_members.image_link)"
            alt="Doctor"
            class="absolute inset-0 w-full h-full object-cover object-top transition-transform duration-700 group-hover:scale-105"
          />
          <div
            class="absolute inset-0 bg-black/10 group-hover:bg-black/30 transition duration-500"
          ></div>
          <div
            class="absolute inset-x-0 bottom-0 translate-y-full group-hover:translate-y-0 transition-transform duration-700 ease-out px-6 pb-6 pt-8 text-white bg-gradient-to-t from-black/90 to-transparent"
          >
            <h3 class="text-lg font-semibold tracking-wide text-white">
              {{ doc.staff_members.full_name }}
            </h3>
            <p class="text-sm text-gray-200 capitalize mt-1">
              {{ doc.speciality.replaceAll("_", " ") | titlecase }}
            </p>
            <div class="flex gap-2 flex-wrap text-xs mt-3">
              <span
                class="px-3 py-1 rounded-full font-medium text-white capitalize tracking-wide"
                [ngClass]="{
                  'bg-blue-600': doc.staff_members.gender === 'MALE',
                  'bg-pink-600': doc.staff_members.gender === 'FEMALE',
                  'bg-gray-600':
                    doc.staff_members.gender !== 'MALE' &&
                    doc.staff_members.gender !== 'FEMALE'
                }"
              >
                {{ "DOCTOR.GENDER." + doc.staff_members.gender | translate }}
              </span>
              <span class="bg-white/20 px-3 py-1 rounded-full capitalize">
                {{ doc.department.replaceAll("_", " ") | titlecase }}
              </span>
            </div>
          </div>
        </a>
        } } @else {
        <div class="col-span-3 text-center py-20">
          <p class="text-gray-400 text-lg">
            {{ "DOCTOR.NOT_FOUND" | translate }}
          </p>
        </div>
        }
      </div>
    </div>

    <!-- Pagination -->
    <div
      class="flex justify-center items-center gap-1 mt-10 flex-wrap text-sm text-gray-700"
    >
      <!-- Prev -->
      <button
        (click)="goToPage(page - 1)"
        [disabled]="page === 1"
        class="px-3 py-1 rounded-md hover:bg-gray-100 transition disabled:opacity-30 disabled:cursor-not-allowed cursor-pointer"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="w-4 h-4 inline-block"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
        {{ "DOCTOR.PAGINATION.PREV" | translate }}
      </button>
      <!-- Page Numbers -->
      @for (i of pageArray; track i) {
      <button
        (click)="goToPage(i + 1)"
        [ngClass]="
          page === i + 1 ? 'bg-gray-200 font-medium' : 'hover:bg-gray-100'
        "
        class="px-3 py-1 rounded-md transition cursor-pointer"
      >
        {{ i + 1 }}
      </button>
      }
      <!-- Next -->
      <button
        (click)="goToPage(page + 1)"
        [disabled]="page === maxPage"
        class="px-3 py-1 rounded-md hover:bg-gray-100 transition disabled:opacity-30 disabled:cursor-not-allowed cursor-pointer"
      >
        {{ "DOCTOR.PAGINATION.NEXT" | translate }}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="w-4 h-4 inline-block"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 5l7 7-7 7"
          />
        </svg>
      </button>
    </div>
  </div>
</body>

<app-footer />

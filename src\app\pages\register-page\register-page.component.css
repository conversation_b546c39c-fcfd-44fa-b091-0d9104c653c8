@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  20%,
  60% {
    transform: translateX(-3px);
  }
  40%,
  80% {
    transform: translateX(3px);
  }
}

.shake {
  animation: shake 0.4s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}
.control.show-error label,
.control:has(.ng-invalid.ng-touched.ng-dirty) label {
  color: red;
}

.control.show-error input,
.control:has(.ng-invalid.ng-touched.ng-dirty) input {
  border-color: red;
}

/* ========== PASSWORD VALIDATION STYLES ========== */
.password-strength-indicator {
  transition: all 0.3s ease;
}

.password-rule-item {
  transition: color 0.2s ease;
}

.password-rule-valid {
  color: #16a34a;
}

.password-rule-invalid {
  color: #dc2626;
}

.password-validation-container {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

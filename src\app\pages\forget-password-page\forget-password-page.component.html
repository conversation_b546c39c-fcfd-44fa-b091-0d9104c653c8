<div
  class="min-h-screen flex items-center justify-center bg-cover bg-center text-black"
  style="background-image: url('./loginBg.png')"
>
  <div
    class="relative w-full max-w-md mx-auto px-7 py-8 bg-white border border-gray-200 rounded-xl shadow-2xl backdrop-blur-lg"
  >
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-bold text-[#4E6688]">Quên mật khẩu</h2>
      <a
        routerLink="/login"
        aria-label="Quay lại"
        class="text-2xl font-bold px-2 hover:text-[#4E6688] transition"
        style="margin-right: -1rem"
        >✕</a
      >
    </div>
    <!-- STEP 1 -->
    @if (step() === 1) {
    <form #phoneForm="ngForm" (ngSubmit)="onSubmitPhone(phoneForm)">
      <div class="mb-4">
        <input
          type="text"
          class="w-full px-3 py-2 border rounded-md outline-none focus:border-[#4E6688]"
          name="phone"
          required
          pattern="^0\d{9}$"
          [ngModel]="phone()"
          (ngModelChange)="phone.set($event)"
          placeholder="Nhập số điện thoại"
        />
      </div>
      <button
        type="submit"
        class="w-full bg-[#4E6688] text-white py-2 rounded-md font-semibold"
        [disabled]="isLoading()"
      >
        Gửi OTP
      </button>
    </form>
    }
    <!-- STEP 2 -->
    @if (step() === 2) {
    <form #resetForm="ngForm" (ngSubmit)="onSubmitReset(resetForm)">
      <div class="mb-3">
        <input
          type="text"
          class="w-full px-3 py-2 border rounded-md outline-none focus:border-[#4E6688]"
          name="otp"
          required
          [ngModel]="otp()"
          (ngModelChange)="otp.set($event)"
          placeholder="Mã OTP"
        />
      </div>
      <div class="mb-3">
        <input
          type="password"
          class="w-full px-3 py-2 border rounded-md outline-none focus:border-[#4E6688]"
          name="newPassword"
          required
          minlength="6"
          [ngModel]="newPassword()"
          (ngModelChange)="newPassword.set($event)"
          placeholder="Mật khẩu mới"
        />
      </div>
      <div class="mb-3">
        <input
          type="password"
          class="w-full px-3 py-2 border rounded-md outline-none focus:border-[#4E6688]"
          name="confirmPassword"
          required
          minlength="6"
          [ngModel]="confirmPassword()"
          (ngModelChange)="confirmPassword.set($event)"
          placeholder="Nhập lại mật khẩu"
        />
      </div>
      <button
        type="submit"
        class="w-full bg-[#4E6688] text-white py-2 rounded-md font-semibold"
        [disabled]="isLoading()"
      >
        Đặt lại mật khẩu
      </button>
    </form>
    }
    <!-- STEP 3 -->
    @if (step() === 3) {
    <div class="text-green-600 font-semibold py-3 text-center">
      {{ successMsg() }}
    </div>
    <a
      routerLink="/login"
      class="w-full mt-2 bg-[#4E6688] text-white py-2 rounded-md font-semibold block text-center"
      >Đăng nhập lại</a
    >
    }
    <!-- Error, Success Message -->
    @if (errorMsg()) {
    <div class="text-red-500 mt-2 text-center">{{ errorMsg() }}</div>
    } @if (successMsg() && step() !== 3) {
    <div class="text-green-500 mt-2 text-center">{{ successMsg() }}</div>
    }
  </div>
</div>

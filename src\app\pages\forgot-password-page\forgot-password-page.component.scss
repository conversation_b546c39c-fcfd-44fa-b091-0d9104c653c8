// Forgot Password Page Styles

.forgot-password-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%);
}

.progress-bar {
  transition: width 0.3s ease-in-out;
}

.form-input {
  transition: all 0.2s ease-in-out;
  
  &:focus {
    outline: none;
    ring: 2px;
    ring-color: #4E6688;
    border-color: transparent;
  }
  
  &.error {
    border-color: #ef4444;
  }
}

.btn-primary {
  background-color: #4E6688;
  transition: all 0.2s ease-in-out;
  
  &:hover:not(:disabled) {
    background-color: #3d5373;
    transform: translateY(-1px);
    box-shadow: 0 10px 25px rgba(78, 102, 136, 0.2);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.card {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(10px);
}

.otp-input {
  text-align: center;
  font-size: 1.5rem;
  letter-spacing: 0.25em;
  font-weight: 600;
}

.password-strength-indicator {
  .strength-bar {
    transition: all 0.3s ease-in-out;
  }
  
  .strength-rule {
    transition: color 0.2s ease-in-out;
    
    &.valid {
      color: #10b981;
    }
    
    &.invalid {
      color: #9ca3af;
    }
  }
}

.success-icon {
  animation: checkmark 0.6s ease-in-out;
}

@keyframes checkmark {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.error-message {
  background-color: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
  padding: 1rem;
  border-radius: 0.75rem;
  border-width: 1px;
  margin-top: 1rem;
}

.back-button {
  color: #6b7280;
  transition: color 0.2s ease-in-out;
  
  &:hover {
    color: #374151;
  }
}

.link-button {
  color: #4E6688;
  transition: color 0.2s ease-in-out;
  
  &:hover {
    color: #3d5373;
  }
}

// Responsive design
@media (max-width: 640px) {
  .card {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .otp-input {
    font-size: 1.25rem;
  }
}

// Loading states
.loading {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Focus states
.form-input:focus-within {
  box-shadow: 0 0 0 3px rgba(78, 102, 136, 0.1);
}

// Hover effects
.btn-primary:hover:not(:disabled) {
  box-shadow: 0 4px 14px 0 rgba(78, 102, 136, 0.39);
}

// Animation for step transitions
.step-content {
  animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

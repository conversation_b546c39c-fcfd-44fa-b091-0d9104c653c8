<!-- ================== WRAPPER ================== -->
<div
  class="min-h-screen flex items-center justify-center bg-cover bg-center"
  style="background-image: url('./loginBg.png')"
>
  <!-- ================== CARD ================== -->
  <div
    class="relative w-full max-w-md mx-auto px-6 py-8 bg-white border border-gray-200 rounded-xl shadow-2xl backdrop-blur-lg"
  >
    <!-- ============= LOGO ============= -->
    <div class="flex flex-col items-center mb-6">
      <img src="./logoNgang.png" alt="GenderCare Logo" class="h-16 mb-2" />
    </div>

    <!-- ============= PROGRESS INDICATOR ============= -->
    <div class="mb-6">
      <div class="flex justify-between items-center mb-2">
        <span class="text-sm font-medium text-gray-700">
          {{ REGISTRATION_STEPS[registrationState().currentStep].title }}
        </span>
        <span class="text-xs text-gray-500">
          {{ getStepProgress().current }}/{{ getStepProgress().total }}
        </span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div
          class="bg-[#4E6688] h-2 rounded-full transition-all duration-300"
          [style.width.%]="getStepProgress().percentage"
        ></div>
      </div>
      <p class="text-xs text-gray-600 mt-2">
        {{ REGISTRATION_STEPS[registrationState().currentStep].description }}
      </p>
    </div>

    <!-- ============= REGISTRATION STEPS ============= -->
    <div class="text-black">
      <!-- ========== STEP 1: PHONE VERIFICATION ========== -->
      @if (registrationState().currentStep === 'phone') {
      <div class="space-y-4">
        <!-- Phone Input -->
        <div class="control relative">
          <input
            type="tel"
            class="peer w-full h-12 px-4 border border-gray-300 rounded-md outline-none text-base bg-transparent focus:border-[#4E6688]"
            [class.border-red-500]="
              registrationState().phoneVerification.phoneError
            "
            id="phone"
            name="phone"
            [value]="registrationState().phoneVerification.phone"
            (input)="onPhoneInput($any($event.target).value)"
            placeholder=""
            maxlength="10"
            autocomplete="tel"
          />
          <label
            for="phone"
            class="absolute left-4 top-3 text-gray-500 pointer-events-none transition-all duration-200 peer-focus:-top-2 peer-focus:text-xs peer-focus:text-[#4E6688] peer-placeholder-shown:top-3 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-500 peer-not-placeholder-shown:-top-2 peer-not-placeholder-shown:text-xs peer-not-placeholder-shown:text-[#4E6688] bg-white px-1"
            >Phone Number</label
          >

          @if (registrationState().phoneVerification.phoneError) {
          <span
            class="absolute right-0 top-[-1.5rem] font-medium tracking-wide text-red-500 text-xs mt-1 ml-1 shake"
          >
            {{ registrationState().phoneVerification.phoneError }}
          </span>
          }
        </div>

        <!-- Send OTP Button -->
        <button
          type="button"
          (click)="sendOTP()"
          [disabled]="
            !registrationState().phoneVerification.isPhoneValid ||
            registrationState().phoneVerification.isSendingOTP
          "
          class="w-full bg-[#4E6688] hover:bg-[#3d5373] text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
        >
          @if (registrationState().phoneVerification.isSendingOTP) {
          <div class="flex items-center justify-center space-x-2">
            <div
              class="animate-spin rounded-full h-5 w-5 border-b-2 border-white"
            ></div>
            <span>Sending...</span>
          </div>
          } @else {
          <span>Send Verification Code</span>
          }
        </button>
      </div>
      }

      <!-- ========== STEP 2: OTP VERIFICATION ========== -->
      @if (registrationState().currentStep === 'otp') {
      <div class="space-y-4">
        <!-- Back Button -->
        <button
          type="button"
          (click)="goBackStep()"
          class="flex items-center text-gray-600 hover:text-gray-800 text-sm mb-4"
        >
          <svg
            class="w-4 h-4 mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 19l-7-7 7-7"
            ></path>
          </svg>
          Back to phone number
        </button>

        <!-- Phone Display -->
        <div class="text-center mb-4">
          <p class="text-sm text-gray-600">Verification code sent to:</p>
          <p class="font-semibold text-gray-800">
            {{
              formatPhoneForDisplay(registrationState().phoneVerification.phone)
            }}
          </p>
        </div>

        <!-- OTP Input -->
        <div class="control relative">
          <input
            type="text"
            class="peer w-full h-12 px-4 border border-gray-300 rounded-md outline-none text-base bg-transparent focus:border-[#4E6688] text-center text-lg tracking-widest"
            [class.border-red-500]="
              registrationState().phoneVerification.otpError
            "
            id="otp"
            name="otp"
            [value]="
              formatOTPForDisplay(registrationState().phoneVerification.otpCode)
            "
            (input)="onOTPInput($any($event.target).value)"
            placeholder="000 000"
            maxlength="7"
            autocomplete="one-time-code"
          />
          <label
            for="otp"
            class="absolute left-4 top-3 text-gray-500 pointer-events-none transition-all duration-200 peer-focus:-top-2 peer-focus:text-xs peer-focus:text-[#4E6688] peer-placeholder-shown:top-3 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-500 peer-not-placeholder-shown:-top-2 peer-not-placeholder-shown:text-xs peer-not-placeholder-shown:text-[#4E6688] bg-white px-1"
            >Verification Code</label
          >

          @if (registrationState().phoneVerification.otpError) {
          <span
            class="absolute right-0 top-[-1.5rem] font-medium tracking-wide text-red-500 text-xs mt-1 ml-1 shake"
          >
            {{ registrationState().phoneVerification.otpError }}
          </span>
          }
        </div>

        <!-- Verify Button -->
        <button
          type="button"
          (click)="verifyOTP()"
          [disabled]="
            registrationState().phoneVerification.otpCode.length !== 6 ||
            registrationState().phoneVerification.isVerifyingOTP
          "
          class="w-full bg-[#4E6688] hover:bg-[#3d5373] text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
        >
          @if (registrationState().phoneVerification.isVerifyingOTP) {
          <div class="flex items-center justify-center space-x-2">
            <div
              class="animate-spin rounded-full h-5 w-5 border-b-2 border-white"
            ></div>
            <span>Verifying...</span>
          </div>
          } @else {
          <span>Verify Code</span>
          }
        </button>

        <!-- Resend OTP -->
        <div class="text-center">
          @if (registrationState().phoneVerification.canResend) {
          <button
            type="button"
            (click)="resendOTP()"
            class="text-sm text-[#4E6688] hover:underline"
          >
            Resend verification code
          </button>
          } @else {
          <p class="text-sm text-gray-500">
            Resend code in
            {{ registrationState().phoneVerification.resendCooldown }}s
          </p>
          }
        </div>
      </div>
      }

      <!-- ========== STEP 3: PASSWORD CREATION ========== -->
      @if (registrationState().currentStep === 'password') {
      <div class="space-y-4">
        <!-- Back Button -->
        <button
          type="button"
          (click)="goBackStep()"
          class="flex items-center text-gray-600 hover:text-gray-800 text-sm mb-4"
        >
          <svg
            class="w-4 h-4 mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 19l-7-7 7-7"
            ></path>
          </svg>
          Back to verification
        </button>

        <div class="control relative mb-4">
          <input
            [type]="showPass ? 'text' : 'password'"
            class="peer w-full h-12 px-4 pr-12 border border-gray-300 rounded-md outline-none text-base bg-transparent focus:border-[#4E6688]"
            id="password"
            name="password"
            [value]="registrationState().formData.password"
            (input)="onPasswordInput($any($event.target).value)"
            placeholder=""
            autocomplete="new-password"
          />
          <label
            for="password"
            class="absolute left-4 top-3 text-gray-500 pointer-events-none transition-all duration-200 peer-focus:-top-2 peer-focus:text-xs peer-focus:text-[#4E6688] peer-placeholder-shown:top-3 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-500 peer-not-placeholder-shown:-top-2 peer-not-placeholder-shown:text-xs peer-not-placeholder-shown:text-[#4E6688] bg-white px-1"
            >Password</label
          >

          <!-- Toggle show/hide password -->
          <button
            type="button"
            (click)="showPass = !showPass"
            class="absolute top-1/2 right-4 -translate-y-1/2 p-0 m-0 bg-transparent border-none cursor-pointer flex items-center justify-center focus:outline-none"
            tabindex="-1"
          >
            @if (!showPass) {
            <img
              src="./visibility_off.png"
              alt="Show password"
              class="w-5 h-5"
            />
            } @else {
            <img src="./visibility.png" alt="Hide password" class="w-5 h-5" />
            }
          </button>
        </div>

        <!-- Password Validation -->
        @if (shouldShowPasswordValidation()) {
        <!-- Password Strength Indicator -->
        <div class="mb-4">
          <div class="flex justify-between items-center mb-1">
            <span class="text-xs text-gray-600">Password Strength:</span>
            <span
              class="text-xs font-medium"
              [style.color]="getPasswordStrengthColor()"
            >
              {{ getPasswordStrengthText() }}
            </span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div
              class="h-2 rounded-full transition-all duration-300"
              [style.background-color]="getPasswordStrengthColor()"
              [style.width.%]="passwordValidation?.password?.score || 0"
            ></div>
          </div>
        </div>

        <!-- Password Rules -->
        <div class="bg-gray-50 rounded-lg p-3 mb-4">
          <div class="text-xs font-medium text-gray-700 mb-2">
            Password Requirements:
          </div>
          @for (rule of passwordValidation?.password?.rules || []; track
          rule.name) {
          <div class="flex items-center text-xs mb-1">
            @if (rule.isValid) {
            <svg
              class="w-3 h-3 text-green-500 mr-2"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clip-rule="evenodd"
              ></path>
            </svg>
            <span class="text-green-700">{{ rule.description }}</span>
            } @else {
            <svg
              class="w-3 h-3 text-red-500 mr-2"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clip-rule="evenodd"
              ></path>
            </svg>
            <span class="text-red-700">{{ rule.description }}</span>
            }
          </div>
          }
        </div>
        }

        <!-- Confirm Password Field -->
        <div class="control relative mb-4">
          <input
            [type]="showConfirmPass ? 'text' : 'password'"
            class="peer w-full h-12 px-4 pr-12 border border-gray-300 rounded-md outline-none text-base bg-transparent focus:border-[#4E6688]"
            [class.border-red-500]="
              passwordValidation?.confirmPassword &&
              !passwordValidation?.confirmPassword?.isValid
            "
            id="confirmPassword"
            name="confirmPassword"
            [value]="registrationState().formData.confirmPassword"
            (input)="onConfirmPasswordInput($any($event.target).value)"
            placeholder=""
            autocomplete="new-password"
          />
          <label
            for="confirmPassword"
            class="absolute left-4 top-3 text-gray-500 pointer-events-none transition-all duration-200 peer-focus:-top-2 peer-focus:text-xs peer-focus:text-[#4E6688] peer-placeholder-shown:top-3 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-500 peer-not-placeholder-shown:-top-2 peer-not-placeholder-shown:text-xs peer-not-placeholder-shown:text-[#4E6688] bg-white px-1"
            >Confirm Password</label
          >

          <!-- Toggle show/hide confirm password -->
          <button
            type="button"
            (click)="showConfirmPass = !showConfirmPass"
            class="absolute top-1/2 right-4 -translate-y-1/2 p-0 m-0 bg-transparent border-none cursor-pointer flex items-center justify-center focus:outline-none"
            tabindex="-1"
          >
            @if (!showConfirmPass) {
            <img
              src="./visibility_off.png"
              alt="Show password"
              class="w-5 h-5"
            />
            } @else {
            <img src="./visibility.png" alt="Hide password" class="w-5 h-5" />
            }
          </button>

          @if (passwordValidation?.confirmPassword &&
          !passwordValidation?.confirmPassword?.isValid) {
          <span
            class="absolute right-0 top-[-1.5rem] font-medium tracking-wide text-red-500 text-xs mt-1 ml-1 shake"
          >
            {{ passwordValidation?.confirmPassword?.error }}
          </span>
          }
        </div>

        <!-- Create Account Button -->
        <button
          type="button"
          (click)="completeRegistration()"
          [disabled]="
            registrationState().isSubmitting ||
            !passwordValidation?.overall?.canSubmit
          "
          class="w-full bg-[#4E6688] hover:bg-[#3d5373] text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
        >
          @if (registrationState().isSubmitting) {
          <div class="flex items-center justify-center space-x-2">
            <div
              class="animate-spin rounded-full h-5 w-5 border-b-2 border-white"
            ></div>
            <span>Creating Account...</span>
          </div>
          } @else {
          <span>Create Account</span>
          }
        </button>

        @if (registrationState().error) {
        <div class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p class="text-sm text-red-600">{{ registrationState().error }}</p>
        </div>
        }
      </div>
      }

      <!-- ========== STEP 4: COMPLETION ========== -->
      @if (registrationState().currentStep === 'complete') {
      <div class="text-center space-y-4">
        <!-- Success Icon -->
        <div
          class="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center"
        >
          <svg
            class="w-8 h-8 text-green-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M5 13l4 4L19 7"
            ></path>
          </svg>
        </div>

        <!-- Success Message -->
        <div>
          <h3 class="text-xl font-semibold text-gray-800 mb-2">
            Registration Complete!
          </h3>
          <p class="text-gray-600">
            Your account has been created successfully.
          </p>
          <p class="text-sm text-gray-500 mt-2">
            Redirecting you to the homepage...
          </p>
        </div>
      </div>
      }
    </div>
    <!-- ========== GOOGLE LOGIN BUTTON ========== -->
    <div class="w-full mt-3 items-center">
      <div class="flex items-center w-full">
        <hr class="flex-grow border-t border-gray-300" />
        <span class="mx-6 text-sm text-gray-500">Or sign up with</span>
        <hr class="flex-grow border-t border-gray-300" />
      </div>
      <div class="mt-2"><app-google /></div>
    </div>

    <!-- ========== END GOOGLE LOGIN BUTTON ========== -->

    <!-- ========== LINK TO LOGIN ========== -->
    <div class="flex justify-end mt-4 text-sm text-gray-700">
      <div class="mx-3">Already have an account?</div>
      <a href="/login" class="text-[#4E6688] font-semibold hover:underline"
        >Sign In</a
      >
    </div>
    <!-- ========== END LINK TO LOGIN ========== -->
  </div>
  <!-- ================== END CARD ================== -->
</div>
<!-- ================== END WRAPPER ================== -->

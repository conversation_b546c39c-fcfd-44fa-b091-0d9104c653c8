<app-header />

<main class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl">
    <!-- Loading State -->
    @if (isLoading) {
    <div class="flex flex-col justify-center items-center py-20">
      <div
        class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"
      ></div>
      @if (isProcessing) {
      <p class="text-blue-600 text-lg font-medium">
        Creating your appointment...
      </p>
      <p class="text-blue-500 text-sm mt-2">
        Please wait while we process your booking
      </p>
      } @else {
      <p class="text-blue-600 text-lg font-medium">Loading...</p>
      }
    </div>
    }

    <!-- Result Content -->
    @if (!isLoading && result) {
    <div class="bg-white rounded-3xl shadow-2xl overflow-hidden text-black">
      <!-- Header Section -->
      <div
        class="px-8 py-10 text-center"
        [class]="
          result.success
            ? 'bg-gradient-to-r from-green-500 to-emerald-600'
            : 'bg-gradient-to-r from-red-500 to-pink-600'
        "
      >
        <!-- Success Icon -->
        @if (result.success) {
        <div
          class="w-20 h-20 mx-auto mb-6 bg-white rounded-full flex items-center justify-center"
        >
          <svg
            class="w-10 h-10 text-green-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M5 13l4 4L19 7"
            ></path>
          </svg>
        </div>
        <h1 class="text-3xl font-bold text-white mb-4">
          {{ "APPOINTMENT.RESULT.SUCCESS_TITLE" | translate }}
        </h1>
        <p class="text-xl text-green-100">
          {{ "APPOINTMENT.RESULT.SUCCESS_MESSAGE" | translate }}
        </p>
        }

        <!-- Error Icon -->
        @if (!result.success) {
        <div
          class="w-20 h-20 mx-auto mb-6 bg-white rounded-full flex items-center justify-center"
        >
          <svg
            class="w-10 h-10 text-red-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            ></path>
          </svg>
        </div>
        <h1 class="text-3xl font-bold text-white mb-4">
          {{ "APPOINTMENT.RESULT.ERROR_TITLE" | translate }}
        </h1>
        <p class="text-xl text-red-100">
          {{ "APPOINTMENT.RESULT.ERROR_MESSAGE" | translate }}
        </p>
        }
      </div>

      <!-- Content Section -->
      <div class="px-8 py-10">
        <!-- Success Details -->
        @if (result.success && result.responseData?.appointment) {
        <div class="space-y-8">
          <!-- Appointment Details Card -->
          <div class="bg-green-50 border border-green-200 rounded-2xl p-6">
            <h3
              class="text-xl font-semibold text-green-800 mb-6 flex items-center"
            >
              <svg
                class="w-6 h-6 mr-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                ></path>
              </svg>
              {{ "APPOINTMENT.RESULT.APPOINTMENT_DETAILS" | translate }}
            </h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Appointment ID -->
              @if (getAppointmentId()) {
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                <div>
                  <p class="text-sm text-gray-600">
                    {{ "APPOINTMENT.RESULT.APPOINTMENT_ID" | translate }}
                  </p>
                  <p class="font-semibold text-gray-800">
                    {{ getAppointmentId() }}
                  </p>
                </div>
              </div>
              }

              <!-- Date -->
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                <div>
                  <p class="text-sm text-gray-600">
                    {{ "APPOINTMENT.RESULT.DATE" | translate }}
                  </p>
                  <p class="font-semibold text-gray-800">
                    {{
                      formatDate(
                        result.responseData?.appointment?.appointment_date || ""
                      )
                    }}
                  </p>
                </div>
              </div>

              <!-- Time -->
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                <div>
                  <p class="text-sm text-gray-600">
                    {{ "APPOINTMENT.RESULT.TIME" | translate }}
                  </p>
                  <p class="font-semibold text-gray-800">
                    {{
                      formatTime(
                        result.responseData?.appointment?.appointment_time || ""
                      )
                    }}
                  </p>
                </div>
              </div>

              <!-- Status -->
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                <div>
                  <p class="text-sm text-gray-600">
                    {{ "APPOINTMENT.RESULT.STATUS" | translate }}
                  </p>
                  <p class="font-semibold text-green-600 capitalize">
                    {{
                      result.responseData?.appointment?.appointment_status ||
                        "pending"
                    }}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Patient Information Card -->
          @if (result.appointmentData) {
          <div class="bg-blue-50 border border-blue-200 rounded-2xl p-6">
            <h3
              class="text-xl font-semibold text-blue-800 mb-6 flex items-center"
            >
              <svg
                class="w-6 h-6 mr-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                ></path>
              </svg>
              {{ "APPOINTMENT.RESULT.PATIENT_INFO" | translate }}
            </h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Full Name -->
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div>
                  <p class="text-sm text-gray-600">
                    {{ "APPOINTMENT.RESULT.FULL_NAME" | translate }}
                  </p>
                  <p class="font-semibold text-gray-800">
                    {{ result.appointmentData.full_name }}
                  </p>
                </div>
              </div>

              <!-- Phone -->
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div>
                  <p class="text-sm text-gray-600">
                    {{ "APPOINTMENT.RESULT.PHONE" | translate }}
                  </p>
                  <p class="font-semibold text-gray-800">
                    {{ result.appointmentData.phone }}
                  </p>
                </div>
              </div>

              <!-- Email -->
              @if (result.appointmentData.email) {
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div>
                  <p class="text-sm text-gray-600">
                    {{ "APPOINTMENT.RESULT.EMAIL" | translate }}
                  </p>
                  <p class="font-semibold text-gray-800">
                    {{ result.appointmentData.email }}
                  </p>
                </div>
              </div>
              }

              <!-- Schedule -->
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div>
                  <p class="text-sm text-gray-600">
                    {{ "APPOINTMENT.RESULT.SCHEDULE" | translate }}
                  </p>
                  <p class="font-semibold text-gray-800">
                    {{ formatSchedule(result.appointmentData.schedule) }}
                  </p>
                </div>
              </div>
            </div>

            <!-- Reason -->
            @if (result.appointmentData.message) {
            <div class="mt-6 pt-6 border-t border-blue-200">
              <p class="text-sm text-gray-600 mb-2">
                {{ "APPOINTMENT.RESULT.REASON" | translate }}
              </p>
              <p class="text-gray-800 bg-white p-4 rounded-lg border">
                {{ result.appointmentData.message }}
              </p>
            </div>
            }
          </div>
          }

          <!-- Next Steps -->
          <div class="bg-yellow-50 border border-yellow-200 rounded-2xl p-6">
            <h3
              class="text-xl font-semibold text-yellow-800 mb-4 flex items-center"
            >
              <svg
                class="w-6 h-6 mr-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                ></path>
              </svg>
              {{ "APPOINTMENT.RESULT.NEXT_STEPS" | translate }}
            </h3>
            <ul class="space-y-2 text-gray-700">
              <li class="flex items-start space-x-3">
                <span class="text-yellow-500 mt-1">•</span>
                <span>{{ "APPOINTMENT.RESULT.STEP1" | translate }}</span>
              </li>
              <li class="flex items-start space-x-3">
                <span class="text-yellow-500 mt-1">•</span>
                <span>{{ "APPOINTMENT.RESULT.STEP2" | translate }}</span>
              </li>
              <li class="flex items-start space-x-3">
                <span class="text-yellow-500 mt-1">•</span>
                <span>{{ "APPOINTMENT.RESULT.STEP3" | translate }}</span>
              </li>
            </ul>
          </div>
        </div>
        }

        <!-- Error Details -->
        @if (!result.success) {
        <div class="space-y-6">
          <!-- Error Message -->
          <div class="bg-red-50 border border-red-200 rounded-2xl p-6">
            <h3 class="text-xl font-semibold text-red-800 mb-4">
              {{ "APPOINTMENT.RESULT.ERROR_DETAILS" | translate }}
            </h3>
            <p
              class="text-red-700 bg-white p-4 rounded-lg border border-red-200"
            >
              {{ result.message }}
            </p>

            @if (result.errorDetails) {
            <div class="mt-4">
              <p class="text-sm text-red-600 mb-2">
                {{ "APPOINTMENT.RESULT.TECHNICAL_DETAILS" | translate }}
              </p>
              <pre
                class="text-xs text-red-600 bg-red-100 p-3 rounded-lg overflow-auto"
                >{{ result.errorDetails }}</pre
              >
            </div>
            }
          </div>

          <!-- Submitted Data (for reference) -->
          @if (result.appointmentData) {
          <div class="bg-gray-50 border border-gray-200 rounded-2xl p-6">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">
              {{ "APPOINTMENT.RESULT.SUBMITTED_DATA" | translate }}
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <strong
                  >{{ "APPOINTMENT.RESULT.FULL_NAME" | translate }}:</strong
                >
                {{ result.appointmentData.full_name }}
              </div>
              <div>
                <strong>{{ "APPOINTMENT.RESULT.PHONE" | translate }}:</strong>
                {{ result.appointmentData.phone }}
              </div>
              @if (result.appointmentData.email) {
              <div>
                <strong>{{ "APPOINTMENT.RESULT.EMAIL" | translate }}:</strong>
                {{ result.appointmentData.email }}
              </div>
              }
              <div>
                <strong
                  >{{ "APPOINTMENT.RESULT.SCHEDULE" | translate }}:</strong
                >
                {{ formatSchedule(result.appointmentData.schedule) }}
              </div>
            </div>
          </div>
          }
        </div>
        }

        <!-- Action Buttons -->
        <div
          class="flex flex-col sm:flex-row gap-4 mt-10 pt-8 border-t border-gray-200"
        >
          <button
            (click)="goToHome()"
            class="flex-1 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-semibold py-4 px-8 rounded-2xl transition-all duration-200 flex items-center justify-center space-x-2 shadow-lg"
          >
            <svg
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
              ></path>
            </svg>
            <span>{{ "APPOINTMENT.RESULT.GO_HOME" | translate }}</span>
          </button>

          @if (!result.success) {
          <button
            (click)="goToAppointment()"
            class="flex-1 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold py-4 px-8 rounded-2xl transition-all duration-200 flex items-center justify-center space-x-2 shadow-lg"
          >
            <svg
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              ></path>
            </svg>
            <span>{{ "APPOINTMENT.RESULT.TRY_AGAIN" | translate }}</span>
          </button>
          }
        </div>
      </div>
    </div>
    }
  </div>
</main>

<app-footer />

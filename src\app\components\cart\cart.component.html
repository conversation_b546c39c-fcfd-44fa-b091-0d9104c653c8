<div class="cart-container">
  <div class="cart-header">
    <h2 class="cart-title">
      🛒
      {{ "TITLE" | translate }}
    </h2>
    <span class="cart-count" *ngIf="cart.itemCount > 0">
      {{ cart.itemCount }} {{ "ITEMS" | translate }}
    </span>
  </div>

  <!-- Empty Cart State -->
  <div class="empty-cart" *ngIf="isCartEmpty()">
    <div class="empty-cart-icon">🛒</div>
    <h3>{{ "EMPTY TITLE" | translate }}</h3>
    <p>{{ "EMPTY MESSAGE" | translate }}</p>
    <a routerLink="/service" class="btn btn-primary">
      {{ "BROWSE SERVICES" | translate }}
    </a>
  </div>

  <!-- Cart Items -->
  <div class="cart-content" *ngIf="!isCartEmpty()">
    <div class="cart-items">
      <div
        class="cart-item"
        *ngFor="let item of cart.items; trackBy: trackByServiceId"
      >
        <div class="item-image">
          <img
            [src]="item.image_link || '/assets/images/default-service.jpg'"
            [alt]="item.service_name"
            onerror="this.src='/assets/images/default-service.jpg'"
          />
        </div>

        <div class="item-details">
          <h4 class="item-name">{{ item.service_name }}</h4>
          <p class="item-description" *ngIf="item.description">
            {{ item.description }}
          </p>
          <div class="item-duration" *ngIf="item.duration">
            ⏰
            {{ formatDuration(item.duration) }}
          </div>
        </div>

        <div class="item-controls">
          <div class="quantity-controls">
            <button
              class="quantity-btn"
              (click)="updateQuantity(item.service_id, item.quantity - 1)"
              [disabled]="item.quantity <= 1"
            >
              −
            </button>
            <span class="quantity">{{ item.quantity }}</span>
            <button
              class="quantity-btn"
              (click)="updateQuantity(item.service_id, item.quantity + 1)"
            >
              +
            </button>
          </div>

          <div class="item-price">
            <span class="unit-price">{{ formatPrice(item.price) }}</span>
            <span class="total-price" *ngIf="item.quantity > 1">
              {{ formatPrice(getItemTotal(item)) }}
            </span>
          </div>

          <button
            class="remove-btn"
            (click)="removeItem(item.service_id)"
            [title]="'REMOVE ITEM' | translate"
          >
            🗑️
          </button>
        </div>
      </div>
    </div>

    <!-- Cart Summary -->
    <div class="cart-summary">
      <div class="summary-row" *ngIf="getTotalDuration() > 0">
        <span class="summary-label">
          ⏰
          {{ "TOTAL DURATION" | translate }}:
        </span>
        <span class="summary-value">{{
          formatDuration(getTotalDuration())
        }}</span>
      </div>

      <div class="summary-row subtotal">
        <span class="summary-label">{{ "SUBTOTAL" | translate }}:</span>
        <span class="summary-value">{{ formatPrice(cart.total) }}</span>
      </div>

      <div class="summary-row total">
        <span class="summary-label">{{ "TOTAL" | translate }}:</span>
        <span class="summary-value">{{ formatPrice(cart.total) }}</span>
      </div>
    </div>

    <!-- Cart Actions -->
    <div class="cart-actions">
      <button class="btn btn-secondary" (click)="clearCart()">
        🗑️
        {{ "CLEAR CART" | translate }}
      </button>

      <a routerLink="/service" class="btn btn-outline">
        ←
        {{ "CONTINUE SHOPPING" | translate }}
      </a>

      <a routerLink="/transaction" class="btn btn-primary">
        💳
        {{ "PROCEED TO PAYMENT" | translate }}
      </a>
    </div>
  </div>
</div>

<div
  class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 px-4 py-8"
>
  <div class="w-full max-w-md bg-white rounded-2xl shadow-xl p-8">
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold text-[#4E6688] mb-2">Reset Password</h1>
      <p class="text-gray-600">Enter your email to reset your password</p>
    </div>

    <!-- Step 1: Email Input -->
    <div
      *ngIf="emailForgotPasswordState().currentStep === 'email'"
      class="space-y-6"
    >
      <div>
        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
          Email Address
        </label>
        <input
          type="email"
          id="email"
          [(ngModel)]="emailForgotPasswordState().emailVerification.email"
          (input)="onEmailInput($event.target.value)"
          placeholder="Enter your email address"
          class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#4E6688] focus:border-transparent transition-all duration-200"
          [class.border-red-500]="
            emailForgotPasswordState().emailVerification.emailError
          "
        />
        <div
          *ngIf="emailForgotPasswordState().emailVerification.emailError"
          class="mt-2 text-sm text-red-600"
        >
          {{ emailForgotPasswordState().emailVerification.emailError }}
        </div>
      </div>

      <button
        type="button"
        (click)="sendOTP()"
        [disabled]="
          !emailForgotPasswordState().emailVerification.isEmailValid ||
          emailForgotPasswordState().emailVerification.isSendingOTP
        "
        class="w-full bg-[#4E6688] hover:bg-[#3d5373] text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
      >
        <span *ngIf="!emailForgotPasswordState().emailVerification.isSendingOTP"
          >Send Verification Code</span
        >
        <span *ngIf="emailForgotPasswordState().emailVerification.isSendingOTP"
          >Sending...</span
        >
      </button>

      <div
        *ngIf="emailForgotPasswordState().emailVerification.otpError"
        class="mt-4 p-4 bg-red-50 border border-red-200 rounded-xl"
      >
        <p class="text-sm text-red-600">
          {{ emailForgotPasswordState().emailVerification.otpError }}
        </p>
      </div>
    </div>

    <!-- Step 2: OTP Verification -->
    <div
      *ngIf="emailForgotPasswordState().currentStep === 'otp'"
      class="space-y-6"
    >
      <div class="text-center mb-6">
        <h2 class="text-xl font-semibold text-[#4E6688] mb-2">
          Verify Your Email
        </h2>
        <p class="text-gray-600">We sent a 6-digit code to</p>
        <p class="font-medium text-[#4E6688]">
          {{ emailForgotPasswordState().emailVerification.email }}
        </p>
      </div>

      <div>
        <label for="otp" class="block text-sm font-medium text-gray-700 mb-2">
          Verification Code
        </label>
        <input
          type="text"
          id="otp"
          [(ngModel)]="emailForgotPasswordState().emailVerification.otpCode"
          (input)="onOTPInput($event.target.value)"
          placeholder="123456"
          maxlength="6"
          class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#4E6688] focus:border-transparent transition-all duration-200 text-center text-2xl tracking-widest"
          [class.border-red-500]="
            emailForgotPasswordState().emailVerification.otpError
          "
        />
        <div
          *ngIf="emailForgotPasswordState().emailVerification.otpError"
          class="mt-2 text-sm text-red-600"
        >
          {{ emailForgotPasswordState().emailVerification.otpError }}
        </div>
      </div>

      <button
        type="button"
        (click)="verifyOTP()"
        [disabled]="
          emailForgotPasswordState().emailVerification.otpCode.length !== 6 ||
          emailForgotPasswordState().emailVerification.isVerifyingOTP
        "
        class="w-full bg-[#4E6688] hover:bg-[#3d5373] text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
      >
        <span
          *ngIf="!emailForgotPasswordState().emailVerification.isVerifyingOTP"
          >Verify Code</span
        >
        <span
          *ngIf="emailForgotPasswordState().emailVerification.isVerifyingOTP"
          >Verifying...</span
        >
      </button>

      <!-- Resend OTP -->
      <div class="text-center">
        <button
          type="button"
          (click)="resendOTP()"
          [disabled]="!emailForgotPasswordState().emailVerification.canResend"
          class="text-[#4E6688] hover:text-[#3d5373] font-medium disabled:text-gray-400 disabled:cursor-not-allowed"
        >
          <span *ngIf="emailForgotPasswordState().emailVerification.canResend"
            >Resend Code</span
          >
          <span *ngIf="!emailForgotPasswordState().emailVerification.canResend">
            Resend in
            {{ emailForgotPasswordState().emailVerification.resendCooldown }}s
          </span>
        </button>
      </div>
    </div>

    <!-- Step 3: New Password -->
    <div
      *ngIf="emailForgotPasswordState().currentStep === 'password'"
      class="space-y-6"
    >
      <div class="text-center mb-6">
        <h2 class="text-xl font-semibold text-[#4E6688] mb-2">
          Create New Password
        </h2>
        <p class="text-gray-600">Enter your new password below</p>
      </div>

      <div>
        <label
          for="newPassword"
          class="block text-sm font-medium text-gray-700 mb-2"
        >
          New Password
        </label>
        <input
          type="password"
          id="newPassword"
          [(ngModel)]="emailForgotPasswordState().formData.newPassword"
          (input)="onPasswordInput($event.target.value)"
          placeholder="Enter new password"
          class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#4E6688] focus:border-transparent transition-all duration-200"
        />
      </div>

      <div>
        <label
          for="confirmPassword"
          class="block text-sm font-medium text-gray-700 mb-2"
        >
          Confirm Password
        </label>
        <input
          type="password"
          id="confirmPassword"
          [(ngModel)]="emailForgotPasswordState().formData.confirmPassword"
          (input)="onConfirmPasswordInput($event.target.value)"
          placeholder="Confirm new password"
          class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#4E6688] focus:border-transparent transition-all duration-200"
        />
      </div>

      <!-- Password Requirements -->
      <div *ngIf="passwordValidation" class="bg-gray-50 rounded-xl p-4">
        <h4 class="text-sm font-medium text-gray-700 mb-3">
          Password Requirements:
        </h4>
        <div class="space-y-2">
          <div
            *ngFor="let rule of passwordValidation.rules | keyvalue"
            class="flex items-center text-sm"
          >
            <span
              [class]="rule.value.isValid ? 'text-green-600' : 'text-gray-400'"
            >
              {{ rule.value.isValid ? "✓" : "○" }}
            </span>
            <span
              class="ml-2"
              [class]="rule.value.isValid ? 'text-green-600' : 'text-gray-400'"
            >
              {{ rule.value.text }}
            </span>
          </div>
        </div>
      </div>

      <button
        type="button"
        (click)="resetPassword()"
        [disabled]="
          !passwordValidation?.overall?.canSubmit ||
          emailForgotPasswordState().isSubmitting
        "
        class="w-full bg-[#4E6688] hover:bg-[#3d5373] text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
      >
        <span *ngIf="!emailForgotPasswordState().isSubmitting"
          >Reset Password</span
        >
        <span *ngIf="emailForgotPasswordState().isSubmitting"
          >Resetting...</span
        >
      </button>

      <div
        *ngIf="emailForgotPasswordState().error"
        class="mt-4 p-4 bg-red-50 border border-red-200 rounded-xl"
      >
        <p class="text-sm text-red-600">
          {{ emailForgotPasswordState().error }}
        </p>
      </div>
    </div>

    <!-- Step 4: Success -->
    <div
      *ngIf="emailForgotPasswordState().currentStep === 'complete'"
      class="text-center space-y-6"
    >
      <div
        class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"
      >
        <svg
          class="w-8 h-8 text-green-600"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M5 13l4 4L19 7"
          ></path>
        </svg>
      </div>
      <h2 class="text-xl font-semibold text-[#4E6688]">
        Password Reset Successful!
      </h2>
      <p class="text-gray-600">
        Your password has been successfully reset. You will be redirected to the
        login page shortly.
      </p>

      <button
        type="button"
        (click)="goToLogin()"
        class="w-full bg-[#4E6688] hover:bg-[#3d5373] text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-200 shadow-lg"
      >
        Go to Login
      </button>
    </div>

    <!-- Back to Login -->
    <div class="text-center mt-6">
      <button
        type="button"
        (click)="goToLogin()"
        class="text-[#4E6688] hover:text-[#3d5373] font-medium"
      >
        ← Back to Login
      </button>
    </div>
  </div>
</div>

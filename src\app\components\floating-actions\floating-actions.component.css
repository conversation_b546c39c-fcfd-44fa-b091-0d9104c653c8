/* Floating Actions Container */
.floating-actions-container {
  position: fixed;
  bottom: 20px;
  right: 1.2rem;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

/* Action Item Wrapper */
.action-item {
  position: relative;
  display: flex;
  align-items: center;
}

/* Base Action Button */
.action-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  transform: scale(1);
}

/* Action Icons */
.action-icon {
  width: 24px;
  height: 24px;
  color: white;
  transition: all 0.3s ease;
  z-index: 2;
  position: relative;
}

.zalo-icon {
  filter: brightness(0) invert(1);
}

.headset-icon {
  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%)
    hue-rotate(202deg) brightness(102%) contrast(97%);
}

.headset-icon-header {
  filter: brightness(0) invert(1);
}

/* Phone Button - WhatsApp Style */
.phone-btn {
  background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
  animation: gentle-pulse 3s ease-in-out infinite;
}

.phone-btn:hover {
  transform: scale(1.15);
  box-shadow: 0 8px 30px rgba(37, 211, 102, 0.4);
  background: linear-gradient(135deg, #2ee86c 0%, #16a085 100%);
}

.phone-btn:hover .action-icon {
  transform: rotate(-10deg) scale(1.1);
}

/* Zalo Button - Zalo Brand Style */
.zalo-btn {
  background: linear-gradient(135deg, #0068ff 0%, #0052cc 100%);
  animation: gentle-pulse 3s ease-in-out infinite 0.5s;
}

.zalo-btn:hover {
  transform: scale(1.15);
  box-shadow: 0 8px 30px rgba(0, 104, 255, 0.4);
  background: linear-gradient(135deg, #1976ff 0%, #0066ff 100%);
}

.zalo-btn:hover .action-icon {
  transform: rotate(10deg) scale(1.1);
}

/* AI Chat Button */
.chat-btn {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  color: #4e6688;
  border: 2px solid #e2e8f0;
}

.chat-btn .action-icon {
  color: #4e6688;
}

.chat-btn:hover {
  transform: scale(1.15);
  box-shadow: 0 8px 30px rgba(78, 102, 136, 0.3);
  background: linear-gradient(135deg, #f8f9fa 0%, #e2e8f0 100%);
}

.chat-btn:hover .action-icon {
  transform: scale(1.2);
  color: #2d3748;
}

/* Back to Top Button */
.back-to-top-btn {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  animation: slide-up 0.3s ease-out;
}

.back-to-top-btn:hover {
  transform: scale(1.15) translateY(-2px);
  box-shadow: 0 8px 30px rgba(99, 102, 241, 0.4);
}

.back-to-top-btn:hover .action-icon {
  transform: translateY(-2px) scale(1.1);
}

/* Ripple Effect */
.ripple-effect {
  position: absolute;
  inset: 0;
  border-radius: 50%;
  opacity: 0.3;
  animation: ripple 2s cubic-bezier(0, 0, 0.2, 1) infinite;
  pointer-events: none;
}

.phone-btn .ripple-effect {
  background: #25d366;
}

.zalo-btn .ripple-effect {
  background: #0068ff;
  animation-delay: 0.5s;
}

.chat-btn .ripple-effect {
  background: #4e6688;
  animation-delay: 1s;
}

.back-to-top-btn .ripple-effect {
  background: #6366f1;
}

/* Unread Badge */
.unread-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 600;
  z-index: 10;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* Animations */
@keyframes gentle-pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0.8);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.1;
  }
  100% {
    transform: scale(1.8);
    opacity: 0;
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tooltips - Real Website Style */
.tooltip {
  position: absolute;
  right: 60px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  z-index: 10000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.tooltip::after {
  content: "";
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border: 6px solid transparent;
  border-left-color: rgba(0, 0, 0, 0.9);
}

.tooltip-text {
  display: block;
  font-weight: 600;
  margin-bottom: 2px;
}

.tooltip-number {
  display: block;
  font-size: 11px;
  opacity: 0.8;
}

.action-item:hover .tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateY(-50%) translateX(-5px);
}

/* AI Chat Interface */
.chat-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  padding: 20px;
}

.chat-container {
  width: 350px;
  height: 500px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: chat-slide-up 0.3s ease-out;
}

.chat-header {
  background: linear-gradient(135deg, #4e6688 0%, #3a5068 100%);
  color: white;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chat-title {
  display: flex;
  align-items: center;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.chat-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.chat-message {
  margin-bottom: 12px;
}

.user-message .message-content {
  background: #4e6688;
  color: white;
  padding: 12px;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.4;
  margin-left: auto;
  max-width: 80%;
}

.bot-message .message-content {
  background: #f1f5f9;
  padding: 12px;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.4;
  max-width: 80%;
}

.message-time {
  font-size: 11px;
  opacity: 0.7;
  margin-top: 4px;
}

/* Typing Indicator */
.typing-indicator {
  background: #f1f5f9;
  padding: 12px;
  border-radius: 12px;
  max-width: 80px;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #64748b;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}
.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Doctor Recommendations */
.doctor-recommendations {
  margin-top: 12px;
  padding: 12px;
  background: #e0f2fe;
  border-radius: 8px;
}

.doctor-list-title {
  font-weight: 600;
  margin-bottom: 8px;
  color: #0369a1;
}

.doctor-card {
  background: white;
  padding: 8px;
  border-radius: 6px;
  margin-bottom: 6px;
  border-left: 3px solid #0369a1;
}

.doctor-name {
  font-weight: 600;
  color: #1e293b;
}

.doctor-specialty {
  font-size: 12px;
  color: #64748b;
}

.quick-replies {
  margin-top: 16px;
}

.quick-reply-title {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 8px;
  font-weight: 500;
}

.quick-reply-btn {
  display: block;
  width: 100%;
  background: white;
  border: 1px solid #e2e8f0;
  padding: 8px 12px;
  border-radius: 8px;
  margin-bottom: 6px;
  font-size: 13px;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s;
}

.quick-reply-btn:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
}

.chat-input {
  padding: 16px;
  border-top: 1px solid #e2e8f0;
  display: flex;
  gap: 8px;
}

.message-input {
  flex: 1;
  border: 1px solid #e2e8f0;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  outline: none;
  resize: none;
  min-height: 36px;
  max-height: 100px;
}

.message-input:focus {
  border-color: #4e6688;
}

.send-btn {
  background: #4e6688;
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.2s;
}

.send-btn:hover:not(:disabled) {
  background: #3a5068;
}

.send-btn:disabled {
  background: #cbd5e1;
  cursor: not-allowed;
}

.clear-btn {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.2s;
  margin-left: 4px;
}

.clear-btn:hover {
  background: #dc2626;
}

@keyframes chat-slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 640px) {
  .floating-actions-container {
    right: 20px;
    bottom: 15px;
  }

  .action-btn {
    width: 45px;
    height: 45px;
  }

  .action-icon {
    width: 20px;
    height: 20px;
  }

  .chat-container {
    width: calc(100vw - 40px);
    height: calc(100vh - 40px);
  }

  .tooltip {
    display: none;
  }
}

@media (min-width: 1200px) {
  .floating-actions-container {
    right: 1.5rem;
  }
}

<app-header />
<body class="w-full bg-[#f8f8f8] py-[3rem]">
  <div class="max-w-7xl mx-auto px-6 pt-7 text-[#4E6688]">
    <app-breadcrumbs />
  </div>
  @defer (when !loading()) { @if (errorMsg(); as err) {
  <div class="text-center py-16 text-red-600">
    <p class="text-lg">{{ err }}</p>
  </div>
  } @else {
  <div class="max-w-7xl mx-auto px-6">
    <!-- === BANNER: Doctor Info + Avatar === -->
    <div
      class="h-[14rem] rounded-3xl px-6 flex items-center bg-cover bg-center mb-8"
      style="background-image: url('./loginBg.png')"
    >
      <img
        [src]="getImageUrl(doctor()?.staff_members?.image_link)"
        class="w-32 h-32 rounded-full border-4 border-white object-cover shadow-lg mr-6 z-10 object-top"
        alt="Doctor Avatar"
      />
      <div class="flex-1 z-10">
        <h2 class="text-4xl font-extrabold text-white mb-2">
          {{ doctorName }}
        </h2>
        <div
          class="flex flex-wrap gap-4 text-base text-[#e2e2e2] font-medium items-center"
        >
          <div class="flex items-center gap-2">
            <svg
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
              ></path>
            </svg>
            <span>{{ doctor()?.staff_members?.working_email }}</span>
          </div>
          <div class="flex items-center gap-2 capitalize">
            <svg
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              ></path>
            </svg>
            <span
              class="px-3 py-1 rounded-full font-medium text-white text-sm"
              [ngClass]="{
                'bg-blue-600': doctor()?.staff_members?.gender === 'MALE',
                'bg-pink-600': doctor()?.staff_members?.gender === 'FEMALE',
                'bg-gray-600':
                  doctor()?.staff_members?.gender !== 'MALE' &&
                  doctor()?.staff_members?.gender !== 'FEMALE'
              }"
            >
              {{
                "DOCTOR.GENDER." +
                  (doctor()?.staff_members?.gender || "OTHER" | uppercase)
                  | translate
              }}
            </span>
          </div>
          <div class="flex items-center gap-2">
            <svg
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
              ></path>
            </svg>
            <span>
              {{ doctor()?.staff_members?.years_experience }}
              {{ "DOCTOR.YEARS_EXP" | translate }}
            </span>
          </div>
          <div class="flex items-center gap-2 capitalize">
            <svg
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"
              ></path>
            </svg>
            <span>{{ specialty }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Tabs Navigation -->
    <div
      class="border-b border-gray-300 flex gap-6 text-base font-semibold mb-6"
    >
      <button
        (click)="setTab('about')"
        [ngClass]="{
          'border-b-2 border-blue-600 text-blue-600': activeTab === 'about',
          'text-gray-600 hover:text-blue-600': activeTab !== 'about'
        }"
        class="pb-2 transition"
      >
        {{ "DOCTOR.TABS.ABOUT" | translate }}
      </button>
      <button
        (click)="setTab('education')"
        [ngClass]="{
          'border-b-2 border-blue-600 text-blue-600': activeTab === 'education',
          'text-gray-600 hover:text-blue-600': activeTab !== 'education'
        }"
        class="pb-2 transition"
      >
        {{ "DOCTOR.TABS.EDUCATION" | translate }}
      </button>
      <button
        (click)="setTab('certifications')"
        [ngClass]="{
          'border-b-2 border-blue-600 text-blue-600':
            activeTab === 'certifications',
          'text-gray-600 hover:text-blue-600': activeTab !== 'certifications'
        }"
        class="pb-2 transition"
      >
        {{ "DOCTOR.TABS.CERTIFICATIONS" | translate }}
      </button>
      <button
        (click)="setTab('license')"
        [ngClass]="{
          'border-b-2 border-blue-600 text-blue-600': activeTab === 'license',
          'text-gray-600 hover:text-blue-600': activeTab !== 'license'
        }"
        class="pb-2 transition"
      >
        {{ "DOCTOR.TABS.LICENSE" | translate }}
      </button>
    </div>

    @switch (activeTab) { @case ('about') {
    <div
      class="w-full bg-white border border-gray-200 rounded-2xl shadow-sm mb-6 p-6 text-left"
    >
      <div class="mb-4">
        <h3
          class="text-xl font-bold text-blue-800 flex items-center gap-2 mb-2"
        >
          <svg
            class="w-6 h-6 text-blue-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
            ></path>
          </svg>
          {{ "DOCTOR.ABOUT.ABOUT" | translate }}
        </h3>
        <div class="text-base text-[#5c5270]">
          {{ doctor()?.about_me?.description }}
        </div>
      </div>
      @if (doctor()?.about_me?.experience) {
      <div class="mb-4">
        <h3
          class="text-xl font-bold text-green-800 flex items-center gap-2 mb-2"
        >
          <svg
            class="w-6 h-6 text-green-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
            ></path>
          </svg>
          {{ "DOCTOR.ABOUT.EXPERIENCE" | translate }}
        </h3>
        <div class="text-base text-[#5c5270]">
          {{ doctor()?.about_me?.experience }}
        </div>
      </div>
      } @if (staffLanguages.length > 0) {
      <div>
        <h3
          class="text-xl font-bold text-purple-800 flex items-center gap-2 mb-2"
        >
          <svg
            class="w-6 h-6 text-purple-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"
            ></path>
          </svg>
          {{ "DOCTOR.ABOUT.LANGUAGES" | translate }}
        </h3>
        <ul class="list-disc list-inside text-base text-[#5c5270] pl-6">
          @for (lang of staffLanguages; track lang) {
          <li>{{ lang }}</li>
          }
        </ul>
      </div>
      }
    </div>
    } @case ('education') {
    <div
      class="w-full bg-white border border-gray-200 rounded-2xl shadow-sm mb-6 p-6 text-left"
    >
      <div class="flex items-center gap-2 mb-3">
        <svg
          class="w-6 h-6 text-blue-500"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
          ></path>
        </svg>
        <h3 class="text-2xl font-bold text-blue-800">
          {{ "DOCTOR.EDUCATION.TITLE" | translate }}
        </h3>
      </div>
      @if (educationDegrees.length > 0) {
      <ul class="divide-y divide-gray-100">
        @for (edu of educationDegrees; track edu.degree) {
        <li class="flex items-center justify-between py-3">
          <span class="font-semibold text-xl text-[#21376b]">
            {{ edu.degree }}
          </span>
          <span class="text-gray-400 text-base font-medium">
            {{ edu.year_completed }}
          </span>
        </li>
        }
      </ul>
      } @else {
      <p class="italic text-gray-400">
        {{ "DOCTOR.EDUCATION.NO_EDUCATION" | translate }}
      </p>
      }
    </div>
    } @case ('certifications') {
    <div
      class="w-full bg-white border border-gray-200 rounded-2xl shadow-sm mb-6 p-6 text-left"
    >
      <div class="flex items-center gap-2 mb-3">
        <svg
          class="w-6 h-6 text-blue-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"
          ></path>
        </svg>
        <h3 class="text-2xl font-bold text-blue-800">
          {{ "DOCTOR.CERTIFICATIONS.TITLE" | translate }}
        </h3>
      </div>
      @if (certificationList.length > 0) {
      <ul class="divide-y divide-gray-100">
        @for (cert of certificationList; track cert.name) {
        <li class="py-3">
          <div class="font-bold text-lg text-[#21376b]">{{ cert.name }}</div>
          <div class="text-gray-500 text-base">
            {{ cert.institution }}
            <span class="mx-2 text-gray-300">|</span>
            {{ "Awarded in" | translate }} {{ cert.year_awarded }}
          </div>
        </li>
        }
      </ul>
      } @else {
      <p class="italic text-gray-400">
        {{ "DOCTOR.CERTIFICATIONS.NO_CERT" | translate }}
      </p>
      }
    </div>
    } @case ('license') {
    <div
      class="relative border-1 border-gray-200 bg-white text-black px-8 py-6 rounded-lg shadow w-full mx-auto"
      style="font-family: 'Times New Roman', serif"
    >
      <h2 class="text-2xl font-bold mb-6 text-blue-800">
        {{ "DOCTOR.LICENSE.TITLE" | translate }}
      </h2>
      <div class="space-y-4 text-lg">
        <div class="flex">
          <span class="w-36 font-semibold">
            {{ "DOCTOR.LICENSE.FULL_NAME" | translate }}:
          </span>
          <span class="flex-1">{{ doctor()?.staff_members?.full_name }}</span>
        </div>
        <div class="flex">
          <span class="w-36 font-semibold">
            {{ "DOCTOR.LICENSE.LICENSE_NO" | translate }}:
          </span>
          <span class="flex-1">{{ doctor()?.license_no }}</span>
        </div>
        <div class="flex">
          <span class="w-36 font-semibold">
            {{ "DOCTOR.LICENSE.SPECIALTY" | translate }}:
          </span>
          <span class="flex-1 capitalize">{{ specialty }}</span>
        </div>
      </div>
    </div>
    } }

    <!-- ==== BLOGS ==== -->
    <div class="mt-16">
      <h3 class="text-2xl font-bold text-[#1e293b] mb-7">
        {{ "DOCTOR.BLOGS_BY_DOCTOR" | translate : { doctor: doctorName } }}
      </h3>
      @if (loading()) {
      <div
        class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8 min-h-[340px]"
      >
        @for (i of [1,2,3]; track i) {
        <div
          class="rounded-2xl bg-gray-100 shadow-sm animate-pulse min-h-[320px]"
        >
          <div class="w-full h-48 bg-gray-200"></div>
          <div class="p-5 space-y-3">
            <div class="h-5 bg-gray-300 rounded w-2/3"></div>
            <div class="h-3 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
        }
      </div>
      } @else if (doctorBlogs.length > 0) {
      <div
        class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8 min-h-[340px]"
      >
        @for (blog of doctorBlogs; track blog.blog_id) {
        <a
          [routerLink]="['/blog', blog.blog_id]"
          class="group block rounded-2xl bg-white border border-gray-200 shadow-sm hover:shadow-lg transition-all duration-200 overflow-hidden relative"
        >
          <div class="relative w-full h-48 bg-gray-50 overflow-hidden">
            <img
              [src]="
                blog.image_link ||
                'https://via.placeholder.com/600x350?text=No+Image'
              "
              class="w-full h-48 object-cover object-center transition-transform duration-300 group-hover:scale-[1.04]"
              alt="Blog thumbnail"
              loading="lazy"
              style="background: #f3f4f6"
            />
          </div>
          <div class="p-6 flex flex-col h-[170px]">
            <h4
              class="font-semibold text-lg mb-1 text-[#21376b] group-hover:text-[#3453b5] transition-colors duration-200 line-clamp-2"
            >
              {{ blog.blog_title }}
            </h4>
            <p class="text-gray-500 text-sm mb-3 flex-1 line-clamp-3">
              {{ blog.excerpt }}
            </p>
            <div class="flex items-center justify-between pt-2">
              <span class="text-xs text-gray-400">
                {{ blog.created_at | date : "dd/MM/yyyy" }}
              </span>
              <svg
                width="18"
                height="18"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                ></path>
              </svg>
            </div>
          </div>
        </a>
        }
      </div>
      } @else {
      <div
        class="min-h-[220px] flex items-center justify-center text-gray-400 italic bg-gray-50 rounded-xl shadow-sm"
      >
        {{ "DOCTOR.NO_BLOGS" | translate }}
      </div>
      }
    </div>
  </div>
  } } @loading {
  <div class="max-w-7xl m-auto px-6 py-16 animate-pulse"></div>
  }
</body>
<app-footer />

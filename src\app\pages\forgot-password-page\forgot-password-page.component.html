<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
  <div class="max-w-md w-full">
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">Reset Password</h1>
      <p class="text-gray-600">Enter your phone number to reset your password</p>
    </div>

    <!-- Progress Indicator -->
    <div class="mb-8">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium text-gray-700">Step {{ getCurrentStepNumber() }} of 4</span>
        <span class="text-sm text-gray-500">{{ getCurrentStepTitle() }}</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div 
          class="bg-[#4E6688] h-2 rounded-full transition-all duration-300"
          [style.width.%]="getProgressPercentage()">
        </div>
      </div>
    </div>

    <!-- Main Card -->
    <div class="bg-white rounded-2xl shadow-xl p-8">
      
      <!-- Step 1: Phone Number Input -->
      <div *ngIf="forgotPasswordState().currentStep === 'phone'">
        <h2 class="text-xl font-semibold text-gray-900 mb-6">Enter Phone Number</h2>
        
        <form #phoneForm="ngForm" (ngSubmit)="sendOTP()">
          <div class="mb-6">
            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
              Phone Number
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              [(ngModel)]="forgotPasswordState().phoneVerification.phone"
              (input)="onPhoneInput($event.target.value)"
              placeholder="0123456789"
              class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#4E6688] focus:border-transparent transition-all duration-200"
              [class.border-red-500]="forgotPasswordState().phoneVerification.phoneError"
              required
            />
            <div *ngIf="forgotPasswordState().phoneVerification.phoneError" 
                 class="mt-2 text-sm text-red-600">
              {{ forgotPasswordState().phoneVerification.phoneError }}
            </div>
          </div>

          <button
            type="submit"
            [disabled]="!forgotPasswordState().phoneVerification.isPhoneValid || forgotPasswordState().phoneVerification.isSendingOTP"
            class="w-full bg-[#4E6688] hover:bg-[#3d5373] text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg">
            <span *ngIf="!forgotPasswordState().phoneVerification.isSendingOTP">Send Verification Code</span>
            <span *ngIf="forgotPasswordState().phoneVerification.isSendingOTP">Sending...</span>
          </button>
        </form>

        <div *ngIf="forgotPasswordState().phoneVerification.otpError" 
             class="mt-4 p-4 bg-red-50 border border-red-200 rounded-xl">
          <p class="text-sm text-red-600">{{ forgotPasswordState().phoneVerification.otpError }}</p>
        </div>
      </div>

      <!-- Step 2: OTP Verification -->
      <div *ngIf="forgotPasswordState().currentStep === 'otp'">
        <h2 class="text-xl font-semibold text-gray-900 mb-2">Verify Code</h2>
        <p class="text-gray-600 mb-6">
          Enter the 6-digit code sent to {{ forgotPasswordState().phoneVerification.phone }}
        </p>

        <form #otpForm="ngForm" (ngSubmit)="verifyOTP()">
          <div class="mb-6">
            <label for="otp" class="block text-sm font-medium text-gray-700 mb-2">
              Verification Code
            </label>
            <input
              type="text"
              id="otp"
              name="otp"
              [(ngModel)]="forgotPasswordState().phoneVerification.otpCode"
              (input)="onOTPInput($event.target.value)"
              placeholder="123456"
              maxlength="6"
              class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#4E6688] focus:border-transparent transition-all duration-200 text-center text-2xl tracking-widest"
              [class.border-red-500]="forgotPasswordState().phoneVerification.otpError"
              required
            />
            <div *ngIf="forgotPasswordState().phoneVerification.otpError" 
                 class="mt-2 text-sm text-red-600">
              {{ forgotPasswordState().phoneVerification.otpError }}
            </div>
          </div>

          <button
            type="submit"
            [disabled]="forgotPasswordState().phoneVerification.otpCode.length !== 6 || forgotPasswordState().phoneVerification.isVerifyingOTP"
            class="w-full bg-[#4E6688] hover:bg-[#3d5373] text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg mb-4">
            <span *ngIf="!forgotPasswordState().phoneVerification.isVerifyingOTP">Verify Code</span>
            <span *ngIf="forgotPasswordState().phoneVerification.isVerifyingOTP">Verifying...</span>
          </button>

          <!-- Resend OTP -->
          <div class="text-center">
            <button
              type="button"
              (click)="resendOTP()"
              [disabled]="!forgotPasswordState().phoneVerification.canResend"
              class="text-[#4E6688] hover:text-[#3d5373] font-medium disabled:text-gray-400 disabled:cursor-not-allowed">
              <span *ngIf="forgotPasswordState().phoneVerification.canResend">Resend Code</span>
              <span *ngIf="!forgotPasswordState().phoneVerification.canResend">
                Resend in {{ forgotPasswordState().phoneVerification.resendCooldown }}s
              </span>
            </button>
          </div>
        </form>
      </div>

      <!-- Step 3: New Password -->
      <div *ngIf="forgotPasswordState().currentStep === 'password'">
        <h2 class="text-xl font-semibold text-gray-900 mb-6">Create New Password</h2>

        <form #passwordForm="ngForm" (ngSubmit)="resetPassword()">
          <div class="mb-4">
            <label for="newPassword" class="block text-sm font-medium text-gray-700 mb-2">
              New Password
            </label>
            <input
              type="password"
              id="newPassword"
              name="newPassword"
              [(ngModel)]="forgotPasswordState().formData.newPassword"
              (input)="onPasswordInput($event.target.value)"
              placeholder="Enter new password"
              class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#4E6688] focus:border-transparent transition-all duration-200"
              required
            />
          </div>

          <div class="mb-4">
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">
              Confirm New Password
            </label>
            <input
              type="password"
              id="confirmPassword"
              name="confirmPassword"
              [(ngModel)]="forgotPasswordState().formData.confirmPassword"
              (input)="onConfirmPasswordInput($event.target.value)"
              placeholder="Confirm new password"
              class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#4E6688] focus:border-transparent transition-all duration-200"
              [class.border-red-500]="passwordValidation?.confirmPassword?.error"
              required
            />
            <div *ngIf="passwordValidation?.confirmPassword?.error" 
                 class="mt-2 text-sm text-red-600">
              {{ passwordValidation.confirmPassword.error }}
            </div>
          </div>

          <!-- Password Strength Indicator -->
          <div *ngIf="shouldShowPasswordValidation()" class="mb-6">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-700">Password Strength</span>
              <span class="text-sm font-medium" [style.color]="getPasswordStrengthColor()">
                {{ getPasswordStrengthText() }}
              </span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
              <div 
                class="h-2 rounded-full transition-all duration-300"
                [style.background-color]="getPasswordStrengthColor()"
                [style.width.%]="passwordValidation?.password?.score || 0">
              </div>
            </div>
            <div class="space-y-1">
              <div *ngFor="let rule of passwordValidation?.password?.rules" 
                   class="flex items-center text-sm">
                <span [class]="rule.isValid ? 'text-green-600' : 'text-gray-400'">
                  {{ rule.isValid ? '✓' : '○' }}
                </span>
                <span class="ml-2" [class]="rule.isValid ? 'text-green-600' : 'text-gray-400'">
                  {{ rule.text }}
                </span>
              </div>
            </div>
          </div>

          <button
            type="submit"
            [disabled]="!passwordValidation?.overall?.canSubmit || forgotPasswordState().isSubmitting"
            class="w-full bg-[#4E6688] hover:bg-[#3d5373] text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg">
            <span *ngIf="!forgotPasswordState().isSubmitting">Reset Password</span>
            <span *ngIf="forgotPasswordState().isSubmitting">Resetting...</span>
          </button>
        </form>

        <div *ngIf="forgotPasswordState().error" 
             class="mt-4 p-4 bg-red-50 border border-red-200 rounded-xl">
          <p class="text-sm text-red-600">{{ forgotPasswordState().error }}</p>
        </div>
      </div>

      <!-- Step 4: Success -->
      <div *ngIf="forgotPasswordState().currentStep === 'complete'" class="text-center">
        <div class="mb-6">
          <div class="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h2 class="text-xl font-semibold text-gray-900 mb-2">Password Reset Successful!</h2>
          <p class="text-gray-600 mb-6">
            Your password has been reset successfully. You will be redirected to the login page shortly.
          </p>
        </div>

        <button
          (click)="goBackToLogin()"
          class="w-full bg-[#4E6688] hover:bg-[#3d5373] text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-200 shadow-lg">
          Go to Login
        </button>
      </div>

      <!-- Back Button -->
      <div *ngIf="forgotPasswordState().currentStep !== 'phone' && forgotPasswordState().currentStep !== 'complete'" 
           class="mt-6 text-center">
        <button
          (click)="goBackStep()"
          class="text-gray-600 hover:text-gray-800 font-medium">
          ← Back
        </button>
      </div>

      <!-- Login Link -->
      <div *ngIf="forgotPasswordState().currentStep === 'phone'" class="mt-6 text-center">
        <p class="text-gray-600">
          Remember your password? 
          <button (click)="goBackToLogin()" class="text-[#4E6688] hover:text-[#3d5373] font-medium">
            Sign In
          </button>
        </p>
      </div>
    </div>
  </div>
</div>

.payment-result-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 0 1rem;
  min-height: 60vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Common Styles */
.loading-state,
.error-state,
.success-state,
.failure-state {
  text-align: center;
  padding: 3rem 2rem;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

/* Loading State */
.loading-spinner {
  font-size: 3rem;
  color: #2c5aa0;
  margin-bottom: 1.5rem;
}

.loading-state h2 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.loading-state p {
  color: #666;
  font-size: 1.1rem;
}

/* Retry Info */
.retry-info {
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 8px;
}

.retry-message {
  margin: 0;
  color: #856404;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.retry-message i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Success State */
.success-icon {
  font-size: 4rem;
  color: #4caf50;
  margin-bottom: 1.5rem;
}

.success-title {
  color: #4caf50;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
}

.success-message {
  color: #333;
  font-size: 1.2rem;
  margin-bottom: 2rem;
  line-height: 1.6;
}

/* Failure State */
.failure-icon {
  font-size: 4rem;
  color: #f44336;
  margin-bottom: 1.5rem;
}

.failure-title {
  color: #f44336;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
}

.failure-message {
  color: #333;
  font-size: 1.2rem;
  margin-bottom: 2rem;
  line-height: 1.6;
}

/* Error State */
.error-icon {
  font-size: 4rem;
  color: #ff9800;
  margin-bottom: 1.5rem;
}

.error-state h2 {
  color: #ff9800;
  font-size: 2rem;
  margin-bottom: 1rem;
}

.error-message {
  color: #333;
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

/* Payment Details */
.payment-details {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  text-align: left;
}

.details-title {
  color: #2c5aa0;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
  text-align: center;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 1rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e0e0e0;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 600;
  color: #555;
  flex: 1;
}

.detail-value {
  color: #333;
  font-weight: 500;
  flex: 1;
  text-align: right;
}

.detail-value.amount {
  color: #2c5aa0;
  font-weight: 700;
  font-size: 1.1rem;
}

/* Action Buttons */
.success-actions,
.failure-actions,
.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 2rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  border: none;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  min-width: 140px;
  justify-content: center;
}

.btn-primary {
  background: #2c5aa0;
  color: white;
}

.btn-primary:hover {
  background: #1e3f73;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(44, 90, 160, 0.3);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  color: #2c5aa0;
  border: 2px solid #2c5aa0;
}

.btn-outline:hover {
  background: #2c5aa0;
  color: white;
}

/* Support Information */
.support-info {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  border: 1px solid #e0e0e0;
}

.support-content h4 {
  color: #2c5aa0;
  font-size: 1.2rem;
  margin: 0 0 1rem 0;
}

.support-content p {
  color: #666;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.support-contacts {
  display: flex;
  gap: 2rem;
  justify-content: center;
  flex-wrap: wrap;
}

.support-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #2c5aa0;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.support-link:hover {
  background: #e3f2fd;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .payment-result-container {
    padding: 0 0.5rem;
  }

  .loading-state,
  .error-state,
  .success-state,
  .failure-state {
    padding: 2rem 1rem;
  }

  .success-title,
  .failure-title {
    font-size: 2rem;
  }

  .success-actions,
  .failure-actions,
  .error-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 300px;
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .detail-value {
    text-align: left;
  }

  .support-contacts {
    flex-direction: column;
    gap: 1rem;
  }

  .payment-details {
    padding: 1.5rem;
  }
}

/* Print Styles */
@media print {
  .success-actions,
  .failure-actions,
  .error-actions,
  .support-info {
    display: none;
  }

  .payment-result-container {
    box-shadow: none;
    margin: 0;
    padding: 0;
  }

  .success-state,
  .failure-state {
    box-shadow: none;
    border: 1px solid #ddd;
  }
}

{"name": "gender-healthcare-fe", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/router": "^19.2.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@primeng/themes": "^19.1.3", "@splidejs/splide": "^4.1.4", "@supabase/supabase-js": "^2.49.8", "@tailwindcss/postcss": "^4.1.8", "angular-oauth2-oidc": "^19.0.0", "date-fns": "^4.1.0", "flyonui": "^2.2.0", "hamburgers": "^1.2.1", "intl-tel-input": "^19.5.7", "jquery": "^3.7.1", "ngx": "^0.0.1", "ngx-intl-tel-input": "^17.0.0", "ngx-slick-carousel": "^19.0.0", "postcss": "^8.5.4", "primeicons": "^7.0.0", "primeng": "^19.1.3", "rxjs": "~7.8.0", "slick-carousel": "^1.8.1", "tailwindcss": "^4.1.8", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.10", "@angular/cli": "^19.2.10", "@angular/compiler-cli": "^19.2.0", "@types/date-fns": "^2.5.3", "@types/jasmine": "~5.1.0", "angular-cli-ghpages": "^2.0.3", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.7.2"}, "overrides": {"@supabase/supabase-js": {"@supabase/gotrue-js": "2.61.0"}}}